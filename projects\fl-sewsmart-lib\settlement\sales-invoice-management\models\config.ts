/* 列表options */
export const listOptions = [
  { label: '发票号', key: 'code', listKey: 'codes', type: 'select' },
  { label: '发票类型', key: 'invoice_type', listKey: 'invoice_types', type: 'select' },
  { label: '客户', key: 'customer_id', listKey: 'customers', type: 'select' },
  { label: '开票日期', key: 'billing_start', listKey: '', type: 'date' },
  { label: '开票人', key: 'billing_user_id', listKey: 'billing_users', type: 'select' },
  { label: '部门', key: 'department_id', listKey: 'departments', type: 'select' },
  { label: '创建人', key: 'gen_user_id', listKey: 'gen_users', type: 'select' },
  { label: '创建时间', key: 'created', listKey: '', type: 'date' },
];

/* 表格配置 */
const headerBaseConfig = { visible: true, type: 'text', width: '112px', sort: false, pinned: false };
export const tableHeader = [
  {
    label: '发票号',
    key: 'code',
    ...headerBaseConfig,
    width: '140px',
  },
  {
    label: '发票类型',
    key: 'type_name',
    ...headerBaseConfig,
  },
  {
    label: '发票税率(%)',
    key: 'tax_rate',
    ...headerBaseConfig,
  },
  {
    label: '客户',
    key: 'customer_name',
    ...headerBaseConfig,
  },
  {
    label: '开票金额(本币)',
    key: 'billing_money_local',
    ...headerBaseConfig,
  },
  {
    label: '开票金额',
    key: 'billing_money',
    ...headerBaseConfig,
  },
  {
    label: '币种',
    key: 'currency_name',
    ...headerBaseConfig,
  },
  {
    label: '开票日期',
    key: 'billing_date',
    ...headerBaseConfig,
    type: 'date',
  },
  {
    label: '应收日期',
    key: 'receivable_date',
    ...headerBaseConfig,
    type: 'date',
  },
  {
    label: '开票人',
    key: 'billing_user_name',
    ...headerBaseConfig,
  },
  {
    label: '部门',
    key: 'department_name',
    ...headerBaseConfig,
  },
  {
    label: '创建人',
    key: 'gen_user_name',
    ...headerBaseConfig,
  },
  {
    label: '创建时间',
    key: 'created_at',
    ...headerBaseConfig,
    type: 'datetime',
  },
  {
    label: '收款状态',
    key: 'invoice_status_name',
    ...headerBaseConfig,
  },
  {
    label: '状态',
    key: 'status_name',
    ...headerBaseConfig,
  },
];

/* 表单配置 */
export const formConfig = [
  {
    label: '发票号',
    key: 'code',
    type: 'input',
    required: true,
    disabled: false,
    maxLength: 20,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    placeholder: '请输入发票号，最多20位',
    validateUnique: true, // 需要校验唯一性
  },
  {
    label: '发票类型',
    key: 'invoice_type',
    type: 'select',
    required: true,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    optionKey: 'invoiceTypeOptions',
    valueKey: 'value',
    labelKey: 'label',
    placeholder: '请选择发票类型',
  },
  {
    label: '客户',
    key: 'customer_id',
    type: 'select',
    required: false,
    disabled: true, // 根据应收单带出，不可修改
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    optionKey: 'customerOptions',
    valueKey: 'id',
    labelKey: 'short_name',
    placeholder: '根据应收单自动带出',
  },
  {
    label: '币种',
    key: 'currency_id',
    type: 'select',
    required: true,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    optionKey: 'currencyOptions',
    valueKey: 'value',
    labelKey: 'label',
    disabled: true, // 根据订单需求自动带出，不可修改
    placeholder: '根据订单需求自动带出',
  },
  {
    label: '应收日期',
    key: 'receivable_date',
    type: 'date',
    required: true,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    placeholder: '请选择应收日期',
  },
  {
    label: '开票日期',
    key: 'billing_date',
    type: 'date',
    required: true,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    placeholder: '请选择开票日期',
    defaultToday: true, // 默认当天
    validateRule: 'receivable_date_gte_billing_date', // 应收日期必须大于等于开票日期
  },
  {
    label: '开票方式',
    key: 'billing_method',
    type: 'select',
    required: false,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    optionKey: 'billingMethodOptions',
    valueKey: 'value',
    labelKey: 'label',
    defaultValue: 3, // 默认数电票
    placeholder: '请选择开票方式',
  },
  {
    label: '开票人',
    key: 'billing_user_id',
    type: 'department-select',
    required: true,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    optionKey: 'organizationOptions',
    valueKey: 'user_id',
    labelKey: 'title',
    filterLeafOnly: true, // 只能选择叶子节点（用户）
    defaultCurrentUser: true, // 默认当前登录用户
    placeholder: '请选择开票人',
  },
  {
    label: '部门',
    key: 'billing_dept_name',
    type: 'input',
    required: false,
    disabled: true, // 不可修改
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    placeholder: '根据开票人自动带出',
  },
  {
    label: '汇率',
    key: 'exchange_rate',
    type: 'input-number',
    required: true,
    min: 0.0001,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    precision: 4,
    disabled: false,
    placeholder: '请输入汇率',
    defaultFromCustomer: true, // 默认从客户档案带出
  },
  {
    label: '税率',
    key: 'tax_rate',
    type: 'input-number',
    required: true,
    min: 0,
    max: 100,
    itemSpan: 6,
    labelSpan: 6,
    controlSpan: 18,
    precision: 2,
    defaultValue: '13', // 默认13%
    placeholder: '请输入税率',
    suffix: '%',
  },
  {
    label: '备注',
    key: 'remark',
    type: 'input-area',
    required: false,
    maxLength: 50,
    itemSpan: 12,
    labelSpan: 3,
    controlSpan: 21,
    placeholder: '请输入备注，最多50字符',
  },
];

/* 关联信息明细表格配置 */
const detailHeaderBaseConfig = {
  visible: true,
  type: 'text',
  width: '120px',
  sort: false,
  pinned: false,
  disable: false,
  resizable: true,
};

export const detailTableHeader = [
  {
    label: '出库单号',
    key: 'outbound_code',
    ...detailHeaderBaseConfig,
    width: '140px',
    pinned: true,
  },
  {
    label: '订单号',
    key: 'order_code',
    ...detailHeaderBaseConfig,
    width: '140px',
    pinned: true,
  },
  {
    label: '交付单号',
    key: 'po_code',
    ...detailHeaderBaseConfig,
    width: '140px',
    pinned: true,
  },
  {
    label: '款号',
    key: 'style_code',
    ...detailHeaderBaseConfig,
    width: '120px',
    pinned: true,
  },
  {
    label: '品名',
    key: 'category',
    ...detailHeaderBaseConfig,
    width: '120px',
    pinned: true,
  },
  {
    label: '应收单号',
    key: 'bills_receivable_code',
    ...detailHeaderBaseConfig,
    width: '140px',
  },
  {
    label: '颜色',
    key: 'color_name',
    ...detailHeaderBaseConfig,
    width: '100px',
  },
  {
    label: '尺码',
    key: 'spec_size',
    ...detailHeaderBaseConfig,
    width: '80px',
  },
  {
    label: '数量',
    key: 'qty',
    ...detailHeaderBaseConfig,
    width: '80px',
    type: 'number',
  },
  {
    label: '单价',
    key: 'price',
    ...detailHeaderBaseConfig,
    width: '100px',
    type: 'currency',
  },
  {
    label: '金额',
    key: 'total_money',
    ...detailHeaderBaseConfig,
    width: '120px',
    type: 'currency',
  },
  {
    label: '应收金额',
    key: 'receivable_money',
    ...detailHeaderBaseConfig,
    width: '120px',
    type: 'currency',
  },
  {
    label: '应收金额(本币)',
    key: 'receivable_money_local',
    ...detailHeaderBaseConfig,
    width: '140px',
    type: 'currency',
  },
  {
    label: '不含税单价',
    key: 'exclude_tax_price',
    ...detailHeaderBaseConfig,
    width: '120px',
    type: 'currency',
  },
  {
    label: '不含税金额',
    key: 'exclude_tax_money',
    ...detailHeaderBaseConfig,
    width: '120px',
    type: 'currency',
  },
  {
    label: '不含税金额(本币)',
    key: 'exclude_tax_local',
    ...detailHeaderBaseConfig,
    width: '140px',
    type: 'currency',
  },
  {
    label: '税额',
    key: 'tax_amount',
    ...detailHeaderBaseConfig,
    width: '100px',
    type: 'currency',
  },
  {
    label: '税额(本币)',
    key: 'tax_amount_local',
    ...detailHeaderBaseConfig,
    width: '120px',
    type: 'currency',
  },
];
