<div class="receivable-selector-drawer">
  <!-- 搜索区域 -->
  <div class="search-container" #searchBarWrap>
    <flc-screen-container [showFold]="true" (reset)="onReset()" (handleFold)="calcTableHeight()">
      <div class="select-box">
        <div *ngFor="let item of searchList" class="search-box">
          <span class="search-label">{{ item.label }}：</span>
          <nz-select
            *ngIf="item.type === 'select'"
            [(ngModel)]="searchParams[item.valueKey]"
            [nzPlaceHolder]="'请选择'"
            nzAllowClear
            [nzDropdownMatchSelectWidth]="false"
            [nzShowSearch]="true"
            (ngModelChange)="loadData()">
            <nz-option *ngFor="let option of optionList[item.optionKey] || []" [nzValue]="option.value" [nzLabel]="option.label">
            </nz-option>
          </nz-select>
          <nz-range-picker
            *ngIf="item.type === 'date'"
            [nzPlaceHolder]="['开始', '结束']"
            [(ngModel)]="searchParams[item.valueKey]"
            (ngModelChange)="onChangeDatePicker()"></nz-range-picker>
        </div>
      </div>
    </flc-screen-container>
  </div>

  <!-- 提示信息和表格标题操作按钮 -->
  <div class="table-header-container">
    <!-- 提示信息 -->
    <!-- <div class="tip-section">
      <nz-alert
        nzType="info"
        nzMessage="请选择同客户同币种的应收单"
        nzShowIcon>
      </nz-alert>
    </div> -->
    <div class="table-title">
      <!-- <div>提示：</div> -->
      <span>
        <i nz-icon [nzIconfont]="'icon-jinggao'"></i>
        <span>请选择同客户同币种的应收单</span>
      </span>
    </div>
    <!-- 表格标题和操作按钮 -->
    <div class="table-header-section">
      <div class="table-title">
        <!-- <span>应收单列表</span>
        <span class="table-subtitle">（展示核销状态为"已核销"且收款状态为"待收款"或"部分收款"的应收单，按最早应收日期顺序排列）</span> -->
      </div>
      <div class="table-actions">
        <span class="selected-count">
          已选中 <span class="count-number">{{ selectedIds.length }}</span> 条数据
        </span>
        <button nz-button nzType="default" nzShape="round" (click)="onClearSelection()" [disabled]="selectedIds.length === 0">清空</button>
        <button nz-button nzType="primary" nzShape="round" (click)="onConfirmSelection()" [disabled]="selectedIds.length === 0">
          批量选择
        </button>
      </div>
    </div>
  </div>

  <!-- 表格 -->
  <div class="table-container">
    <flc-table
      #flcTable
      [tableHeader]="tableHeader"
      [tableConfig]="tableConfig"
      [template]="tableTemplate"
      (indexChanges)="onPageIndexChange($event)"
      (sizeChanges)="onPageSizeChange($event)"
      (getCount)="onSelectedChange($event)">
    </flc-table>

    <!-- 表格模板 -->
    <ng-template #tableTemplate let-data="data">
      <ng-container *ngIf="data.isTd">
        <!-- 关联发票 -->
        <ng-container *ngIf="data.key === 'related_invoice'">
          <span *ngIf="data.item.related_invoice">{{ data.item.related_invoice }}</span>
          <span *ngIf="!data.item.related_invoice">-</span>
        </ng-container>

        <!-- 关联单据 -->
        <ng-container *ngIf="data.key === 'related_document'">
          <span *ngIf="data.item.related_document">{{ data.item.related_document }}</span>
          <span *ngIf="!data.item.related_document">-</span>
        </ng-container>

        <!-- 关联款号 -->
        <ng-container *ngIf="data.key === 'related_style_code'">
          <span *ngIf="data.item.related_style_code">{{ data.item.related_style_code }}</span>
          <span *ngIf="!data.item.related_style_code">-</span>
        </ng-container>

        <!-- 应收金额 -->
        <ng-container *ngIf="data.key === 'expected_amount'">
          {{ data.item.expected_amount | currency: '':'symbol':'1.2-2' }}
        </ng-container>

        <!-- 已收金额 -->
        <ng-container *ngIf="data.key === 'received_amount'">
          {{ data.item.received_amount | currency: '':'symbol':'1.2-2' }}
        </ng-container>

        <!-- 待收金额 -->
        <ng-container *ngIf="data.key === 'pending_amount'">
          {{ data.item.pending_amount | currency: '':'symbol':'1.2-2' }}
        </ng-container>

        <!-- 应收日期 -->
        <ng-container *ngIf="data.key === 'except_time'">
          <span *ngIf="data.item.except_time && data.item.except_time.length">
            {{ data.item.except_time.join('、') | date: 'yyyy-MM-dd'}}
          </span>
          <span *ngIf="!data.item.except_time && !data.item.except_time">-</span>
        </ng-container>

        <!-- 创建时间 -->
        <ng-container *ngIf="data.key === 'create_time'">
          {{ data.item.create_time | date: 'yyyy-MM-dd HH:mm' }}
        </ng-container>
      </ng-container>

      <!-- 操作列 -->
      <ng-container *ngIf="data.isAction">
        <button nz-button nzType="link" nzSize="small" (click)="onSelectSingle(data.item)">选择</button>
      </ng-container>
    </ng-template>
  </div>
</div>
