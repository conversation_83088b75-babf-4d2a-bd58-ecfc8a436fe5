import { OutboundOrderTypeEnum, OutboundStatusEnum } from './outstock.enum';

// 模块按钮权限
export interface UserActions {
  create: boolean; // 新建
}
export interface SearchConfig<T, U> {
  label: string;
  value: keyof T;
  optionKey?: keyof U;
  type: string;
  modes?: OutboundOrderTypeEnum[];
}

export interface FormConfig<T, U> {
  label: string;
  code: keyof T;
  optionKey?: keyof U;
  required: boolean;
  span: number;
  labelSpan: number;
  maxLength?: number;
  type: 'input' | 'select' | 'date' | 'radio' | 'checkbox' | 'textarea';
  disable?: boolean;
  placeholder?: string;
  hide?: boolean;
}

export interface CommonOption {
  label: string;
  value: string | string[];
  type?: string;
  source_factory_code?: string;
}

export interface ColorInfo {
  /**
   * 颜色code
   */
  color_code?: string;
  /**
   * 颜色id
   */
  color_id?: string;
  /**
   * 颜色name
   */
  color_name?: string;
}

export interface WhLocation {
  /**
   * 位置名称 由仓库区域、仓库位置、仓库货架、仓库层组成
   */
  location_name?: string;
  /**
   * 位置id 由 仓库区域id-仓库位置id-仓库货架id-仓库层id 组成
   */
  value?: string;
  /**
   * 仓库层id
   */
  warehouse_layer_id?: string;
  /**
   * 仓库位置id
   */
  warehouse_position_id?: string;
  /**
   * 仓库区域id
   */
  warehouse_section_id?: string;
  /**
   * 仓库货架id
   */
  warehouse_shelves_id?: string;
}

export interface SizeInfo {
  /**
   * 尺码code
   */
  spec_code?: string;
  /**
   * 尺码id
   */
  spec_id?: string;
  /**
   * 尺码name
   */
  spec_size?: string;
}

export interface DocCodesInfo {
  doc_code?: string;
  doc_type?: string;
  doc_code_name?: string;
  doc_type_name?: string;
  source_factory_code?: string;
}

export interface StockListOption {
  /**
   * 出库单下拉框
   */
  codes?: CommonOption[];
  /**
   * 出库单状态
   */
  outbound_status?: CommonOption[];
  /**
   * 出库类型下拉框
   */
  outbound_types?: CommonOption[];
  /**
   * 创建人下拉框
   */
  user_ids?: CommonOption[];
  /**
   * 仓库下拉框
   */
  warehouse_ids?: CommonOption[];
  /**
   * 关联单据下拉框
   */
  doc_codes?: CommonOption[];

  customers?: CommonOption[];
  suppliers?: CommonOption[];
  style_codes?: CommonOption[];
}

export interface StockSearchData {
  /**
   * 出库单号
   */
  code?: string;
  /**
   * 出库类型
   */
  outbound_type?: string;
  /**
   * 关联单据value
   */
  doc_code?: string;
  /**
   *  关联单据 type类型
   */
  doc_type?: string;
  /**
   * 出库单状态
   */
  outbound_status?: string;
  /**
   * 出库时间
   */
  outbound_time?: string;
  outbound_time_end?: string;
  outbound_time_start?: string;
  /**
   * 出库仓库id
   */
  warehouse_id?: string;
  /**
   * 创建人id
   */
  user_id?: string;
  /**
   * 创建时间
   */
  gen_time?: string;
  gen_time_end?: string;
  gen_time_start?: string;
  /**
   * 页码
   */
  page?: string;
  /**
   * 每页数量
   */
  size?: string;
  style_code?: string;
  customer_id?: string;
  supplier_id?: string;
}

export interface StockItem {
  /**
   * 出库单号
   */
  code?: string;
  /**
   * 是否可编辑
   */
  edit?: boolean;
  /**
   * 创建时间
   */
  gen_time?: string;
  /**
   * 创建人id
   */
  gen_user_id?: string;
  /**
   * 创建人名称
   */
  gen_user_name?: string;
  /**
   * 出库单id
   */
  id?: string;
  /**
   * 出库状态
   */
  outbound_status?: OutboundStatusEnum;
  /**
   * 出库状态名称
   */
  outbound_status_name?: string;
  /**
   * 出库时间
   */
  outbound_time?: string;
  /**
   * 出库类型
   */
  outbound_type?: string;
  /**
   * 出库类型名称
   */
  outbound_type_name?: string;
  /**
   * 仓库id
   */
  warehouse_id?: string;
  /**
   * 仓库名称
   */
  warehouse_name?: string;
  /**
   * 关联单据
   */
  doc_codes?: DocCodesInfo[];
  /** 格式化单据文本 */
  doc_codes_text?: string;

  product_type: number;
  customer_name?: string;
  supplier_name?: string;
  style_code?: string;
  product_name?: string;
  total_qty?: string;
  category?: string;
}

/**
 * 选择成品列表
 */
export interface ProductItem {
  /**
   * 唯一标识
   */
  id?: string;
  /**
   * 款式编码
   */
  style_code?: string;

  order_code?: string;
  currency_id?: number;
  currency_name?: string;
  order_uuid?: string;
  po_uuid?: string;
  po_code?: string;
  customer?: string;
  factory_code?: string;
  factory_name?: string;
  order_qty?: string;
  allow_outbound_qty: string;
  over_qty?: string;

  /**
   * 品名
   */
  category?: string;
  /**
   * 成品类型
   */
  product_type?: string;
  /**
   * 成品类型名称
   */
  product_type_name?: string;
  /**
   * 供应商名称
   */
  supplier_name?: string;
  /**
   * 供应商编码
   */
  supplier_code?: string;
  /**
   * 供应商id
   */
  supplier_id?: string;
  /**
   * 供应商货号
   */
  supplier_art_num?: string;
  /**
   * 颜色
   */
  color?: ColorInfo;
  /**
   * 尺码
   */
  size?: SizeInfo;
  /**
   * 仓库名称
   */
  warehouse_name?: string;
  /**
   * 仓库id
   */
  warehouse_id?: string;
  /**
   * 货位
   */
  location?: WhLocation;
  /**
   * 库存数量
   */
  inventory_qty?: string;
  /**
   * 款号sku id
   */
  product_sku_id?: string;
  /**
   * sku id 类型
   */
  product_sku_type?: string;
  /**
   * 箱号
   */
  box_num?: string;
  /**
   * 箱数
   */
  box_qty?: string;
  /**
   * 出库数量
   */
  outbound_qty?: string;

  /**
   * 是否可编辑
   */
  edit: true;

  /**
   * 跟踪卡号
   */
  tracking_card_code?: string;

  /**
   * 跟踪来源工厂
   */
  source_factory_code?: string;

  /**
   * 出库方式 1按件 2按箱子 只有pad上按箱子
   */
  method?: string;

  factory?: string;
}

export interface ProductListOption {
  /**
   * 款式编码
   */
  style_skus?: CommonOption[];
  /**
   * 品名
   */
  category_skus?: CommonOption[];
  /**
   * 成品类型
   */
  product_types?: CommonOption[];
  /**
   * 供应商
   */
  supplier_skus?: CommonOption[];
  /**
   * 供应商货号
   */
  supplier_art_num_skus?: CommonOption[];
  /**
   * 颜色
   */
  colors?: CommonOption[];
  /**
   * 尺码
   */
  sizes?: CommonOption[];
  /**
   * 货位下拉框
   */
  locations?: WhLocation[];

  order_uuids: CommonOption[];

  customers: CommonOption[];

  factory_list: CommonOption[];
}

export interface ProductSearchData {
  /**
   * 品名sku
   */
  category_sku_ids?: string[];
  /**
   * 颜色
   */
  color_sku_ids?: string[];
  location?: WhLocation;
  /**
   * 出库类型
   */
  outbound_type?: string;
  /**
   * 页码
   */
  page?: string;
  /**
   * 成品类型
   */
  product_type?: string;
  /**
   * 每页数量
   */
  size?: string;
  /**
   * 尺码
   */
  size_sku_ids?: string[];
  /**
   * 款式编码
   */
  style_sku_ids?: string[];
  /**
   * 供应商货号sku
   */
  supplier_art_num_sku_ids?: string[];
  /**
   * 供应商sku
   */
  supplier_sku_ids?: string[];
  /**
   * 仓库id
   */
  warehouse_id?: string;

  order_uuid?: string[];

  customer_id?: string;

  factory?: string;
}

export interface OutboundInfo {
  /**
   * 出库单id
   */
  id?: string;
  /**
   * 版本号
   */
  version?: number;
  /**
   * 出库单号
   */
  code?: string;
  /**
   * 出库类型
   */
  outbound_type?: string;
  /**
   * 产品类型
   */
  product_type?: string;
  product_type_name?: string;
  /**
   * 客户
   */
  customer_id?: string;
  customer_name?: string;
  /**
   * 出库仓库name
   */
  warehouse_name?: string;
  /**
   * 出库仓库id
   */
  warehouse_id?: string;
  /**
   * 备注
   */
  remark?: string;
  /**
   * 出库状态
   */
  outbound_status?: string;
  /**
   * 出库时间
   */
  outbound_time?: string;
  /**
   * 创建时间
   */
  gen_time?: string;
  /**
   * 创建人id
   */
  gen_user_id?: string;
  /**
   * 创建人名称
   */
  gen_user_name?: string;
  /**
   * 出库状态名称
   */
  outbound_status_name?: string;
  /**
   * 出库类型名称
   */
  outbound_type_name?: string;
  /**
   * 是否可编辑
   */
  edit?: boolean;
  /**
   * 出库单行
   */
  data_list?: OutboundSaveLine[];
  /**
   * 删除的出库单行id
   */
  delete_ids?: string[];
  /**
   * 是否提交
   */
  commit?: boolean;
  /**
   * "生成终端" pad web
   */
  gen_terminal?: string;
  /**
   * 关联单据
   */
  doc_codes?: {
    doc_code: string;
    doc_type: number;
    doc_code_name: string;
    doc_type_name: string;
    source_factory_code: string;
  };
  data_type?: OutboundOrderTypeEnum;
}

export interface OutboundSaveLine extends ProductItem {
  /**
   * 出库数量
   */
  outbound_qty?: string;
  /**
   * 箱号
   */
  box_number?: string;

  allow_outbound_qty: string;

  order_qty: string;

  already_outbound_qty: string;

  /**
   * 含税单价
   */
  unit_price?: string | number;

  /**
   * 金额
   */
  amount?: string;
}
