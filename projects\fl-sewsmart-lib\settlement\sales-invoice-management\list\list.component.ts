import { Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { FlcModalService, FlcTableComponent, resizable } from 'fl-common-lib';
import { endOfDay, format, startOfDay } from 'date-fns';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SalesInvoiceService } from '../sales-invoice.service';
import { listOptions, tableHeader } from '../models/config';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { NzModalService } from 'ng-zorro-antd/modal';
import { ReturnModelComponent } from '../../components/return-model/return-model.component';
import { finalize } from 'rxjs';

enum StatusEnum {
  all = 0,
  /** 待提交 */
  wait_submit = 1,
  /** 待审核 */
  wait_examine = 2,
  /** 已核销 */
  completed = 3,
}
@Component({
  selector: 'flss-sales-invoice-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.scss'],
})
@resizable()
export class SalesInvoiceListComponent implements OnInit {
  @ViewChild('searchHeader') hearderComponent?: ElementRef;
  @ViewChild('tableRef') tableRef!: FlcTableComponent;

  checkedInfo!: { count: number; list: any[] };
  checkData = [
    {
      label: '全部',
      value: StatusEnum.all,
      isTotalItem: true,
      amount: '0',
    },
    {
      label: '待提交',
      value: StatusEnum.wait_submit,
      amount: '0',
    },
    {
      label: '待审核',
      value: StatusEnum.wait_examine,
      amount: '0',
    },
    {
      label: '已核销',
      value: StatusEnum.completed,
      amount: '0',
    },
  ];

  searchList = listOptions;
  tableHeader = tableHeader;
  searchData: any = {};

  tableConfig: any = {
    tableName: 'sales-invoice-list',
    dataList: [],
    count: null,
    height: 500,
    loading: false,
    pageSize: 20,
    pageIndex: 1,
    actionWidth: '100px',
    settingBtnPos: 'start',
    version: 1.2,
    detailBtn: true,
    hasCheckbox: true,
  };

  isExporting = false;
  env_project = 'sewsmart'; //默认系统环境变量
  userActions: any = [];
  optionMaps = {};

  constructor(
    @Inject('environment') env: any,
    private _service: SalesInvoiceService,
    private _router: Router,
    private _msg: NzMessageService,
    private _activeRoute: ActivatedRoute,
    private _notice: NzNotificationService,
    private _flcModalService: FlcModalService,
    private _modalService: NzModalService // private _nzModalRef: NzModalRef
  ) {
    env.project && (this.env_project = env.project);
  }

  ngOnInit() {
    (this as any).addResizePageListener();
    this.userActions = this._service.getUserActions();
    this.getList();
    this.getOptions();
  }

  ngOnDestroy(): void {
    (this as any).removeResizePageListener();
  }

  ngAfterViewInit(): void {
    this.resizePage();
  }

  resizePage() {
    const headerHeight = this.hearderComponent?.nativeElement?.offsetHeight ?? 32;
    this.tableConfig.height = window.innerHeight - headerHeight - 140;
    this.tableConfig = { ...this.tableConfig };
  }

  /**
   * 获取列表
   * @param  {} reset=false // 是否重置
   */
  getList(reset?: boolean) {
    this.tableConfig.loading = true;
    if (reset) {
      this.resetSearch();
      this.tableConfig.pageIndex = 1;
    }
    const data = {
      ...this.handleWhere(),
      size: this.tableConfig.pageSize,
      page: this.tableConfig.pageIndex,
    };
    this._service
      .getList(data)
      .pipe(
        finalize(() => {
          if (this.tableConfig.loading) {
            this.tableConfig = { ...this.tableConfig, loading: false };
          }
        })
      )
      .subscribe((res: any) => {
        this.tableConfig = { ...this.tableConfig, loading: false, count: res.data.total, dataList: res.data.list };
        if (!data.status) {
          this.checkData = res.data.status.map((item: any) => {
            return {
              ...item,
              label:item.status_label,
              value:item.status,
  
              amount: item.status_count,
            };
          });
        }
        // res.data.status?.forEach((item: any) => {
        //   const target = this.checkData.find((e) => e.value === item.status);
        //   if (target) {
        //     target.amount = item.status_count;
        //   }
        // });
      });
  }

  resetSearch() {
    this.searchData = {
      ...this.searchData,
      code: null, // 发票号
      invoice_type: null, // 发票类型
      customer_id: null, // 客户id
      billing_user_id: null, // 开票人id
      department_id: null, // 部门id
      billing: [], // 开票时间
      created: [], // 创建时间
    };
  }

  handleWhere() {
    const where: any = {};
    Object.entries(this.searchData).forEach((item: any) => {
      if (item[1]) {
        switch (item[0]) {
          case 'billing_start':
            where['billing_start_at'] = item[1][0] ? Number(format(startOfDay(item[1][0]), 'T')) : null;
            where['billing_end_at'] = item[1][1] ? Number(format(endOfDay(item[1][1]), 'T')) : null;
            break;
          case 'created':
            where['created_start_at'] = item[1][0] ? Number(format(startOfDay(item[1][0]), 'T')) : null;
            where['created_end_at'] = item[1][1] ? Number(format(endOfDay(item[1][1]), 'T')) : null;
            break;
          default:
            where[item[0]] = item[1];
            break;
        }
      }
    });
    return where;
  }

  getOptions() {
    this._service.getListOptions().subscribe((res: any) => {
      this.optionMaps = res.data;
    });
  }
  onInputSearchValue(e: any) {
    this.searchData.keywords = e;
    this.tableConfig.pageIndex = 1;
    this.getList();
  }

  onStatusChanged(e: any) {
    this.searchData.status = e?.[0] || null;
    this.tableConfig.pageIndex = 1;
    this.getList();
  }

  modelChanges() {
    this.tableConfig.pageIndex = 1;
    this.getList();
  }

  /**
   * 页码改变
   * @param  {number} e
   */
  indexChanges(e: number) {
    this.tableConfig.pageIndex = e;
    this.getList();
  }
  /**
   * 页数改变
   * @param  {number} e
   */
  sizeChanges(e: number) {
    this.tableConfig.pageIndex = 1;
    this.tableConfig.pageSize = e;
    this.getList();
  }

  selectedIds = [];
  // 选中的数量
  onSelectedCount(e: any) {
    this.checkedInfo = e;
    this.selectedIds = e.list.map((item: any) => item.id);
  }

  clearAllSelected() {
    this.tableRef.clearAllSelected();
    this.tableConfig = { ...this.tableConfig };
  }

  // 获取创建人和创建时间
  _getCreated_info(param: any) {
    const created_at = param?.created_at ? format(param?.created_at, 'yyyy/MM/dd HH:mm:ss') : null
    return `${param?.gen_user_name}/${created_at}`
  }

  /**
   * 跳转详情
   * @param  {any} id
   */
  getDetails(id: any) {
    this._router.navigate(['../list', id], { relativeTo: this._activeRoute });
  }

  // 审核按钮操作
  onAudit(action: 'pass' | 'reject') {
    if (!this.checkedInfo.count) {
      this._notice.error('请至少选择一条数据', '');
      return false;
    }
    const _invalidCodes: string[] = [];
    const _ids: number[] = [];
    this.checkedInfo.list.forEach((item) => {
      item.status !== StatusEnum.wait_examine ? _invalidCodes.push(item.code) : _ids.push(item.id);
    });
    if (_invalidCodes.length) {
      this._msg.warning(`发票号${_invalidCodes.join('、')}不能操作${action === 'pass' ? '审核通过' : '退回修改'}`, { nzDuration: 10000 });
    }
    if (!_ids.length) return;
    return action === 'pass' ? this._onPass(_ids) : this._onReject(_ids);
  }

  _onPass(_ids: number[]) {
    const ref = this._flcModalService.confirmCancel({ content: '确定审核通过' });
    ref.afterClose.subscribe((res) => {
      if (res) {
        this._service.batchPass(_ids).subscribe((res) => {
          if (res.data) {
            this._msg.success('审核通过');
            this.clearAllSelected();
            this.getOptions();
            this.getList(true);
          }
        });
      }
    });
  }

  _onReject(_ids: number[]) {
    this._modalService.create({
      nzContent: ReturnModelComponent,
      nzWidth: 400,
      nzClosable: false,
      nzWrapClassName: 'flc-confirm-modal',
      nzFooter: null,
      nzOnOk: (comp: any) => {
        const _value = comp.formGroup.getRawValue();
        this._service.batchReject(_ids, _value.reason).subscribe((result) => {
          if (result.data) {
            this._msg.success('退回成功');
            this.clearAllSelected();
            this.getOptions();
            this.getList(true);
          }
        });
      },
    });
  }

  // 删除行
  deleteLine(id: any) {
    const ref = this._flcModalService.confirmCancel({
      strongConfirm: true,
      content: '确定删除该销售发票？',
    });
    ref.afterClose.subscribe((res: any) => {
      if (res) {
        this._service.delete(id).subscribe((res) => {
            if (res.code === 200) {
              this._msg.success('删除通过');
              this.clearAllSelected();
              this.getOptions();
              this.getList();
            }
          });
      }
    });
  }
}
