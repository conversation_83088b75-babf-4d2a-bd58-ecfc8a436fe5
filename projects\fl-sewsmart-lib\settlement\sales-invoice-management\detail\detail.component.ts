import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil, finalize } from 'rxjs';
import { FlcTableComponent, FlcTableHelperService, FlcUtilService } from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { SalesInvoiceService } from '../sales-invoice.service';
import { formConfig, detailTableHeader } from '../models/config';
import { OrderStatus, PageEditStatusEnum } from '../models/sales-invoice.enum';
import {
  SalesDetailInterface,
  SalesInvoiceDetailItem,
  SalesCommitParams,
  SalesCommitOutboundItem,
} from '../models/sales-invoice.interface';
import { SalesRefuseComponent } from '../components/refuse-modal/refuse-modal.component';

@Component({
  selector: 'flss-sales-invoice-detail',
  templateUrl: './detail.component.html',
  styleUrls: ['./detail.component.scss'],
  providers: [TranslatePipe],
})
export class SalesInvoiceDetailComponent implements OnInit, OnDestroy {
  @ViewChild('detailTableRef', { static: false }) detailTableRef!: FlcTableComponent;

  private destroy$ = new Subject<void>();
  translateName = 'salesInvoice.';
  PageEditStatusEnum = PageEditStatusEnum;
  pageMode = PageEditStatusEnum.read;
  OrderStatus = OrderStatus;
  orderStatus: OrderStatus = 1; // 当前订单状态
  loading = false;
  saving = false; // 提交保存状态
  formConfig = formConfig;
  salesForm!: FormGroup;
  userActions: string[] = [];
  salesId?: number;
  showDeptField = false; // 控制部门字段是否显示

  detail: SalesDetailInterface = {} as SalesDetailInterface;

  // 关联信息表格配置
  detailTableHeader = detailTableHeader;
  detailTableConfig: any = {
    tableName: 'sales-invoice-detail-table',
    dataList: [],
    height: 400,
    loading: false,
    settingBtnPos: 'start',
    version: '1.0.0',
    hasAction: true,
    detailBtn: false,
    actionWidth: '80px',
    hasCheckbox: false,
  };

  // 扁平化的明细数据
  flatDetailList: SalesInvoiceDetailItem[] = [];

  // 选项数据
  organizationOptions: any[] = [];
  customerOptions: any[] = [];
  invoiceTypeOptions: any[] = [];
  billingMethodOptions: any[] = [];
  currencyOptions: any[] = [];

  // 表格相关
  btnHighLight = false;
  headers: any[] = detailTableHeader;

  constructor(
    private _fb: FormBuilder,
    private _service: SalesInvoiceService,
    private _route: ActivatedRoute,
    private _router: Router,
    private _message: NzMessageService,
    private _modal: NzModalService,
    private _cdr: ChangeDetectorRef,
    private _tableHelper: FlcTableHelperService,
    private _flcUtil: FlcUtilService,
    private _translate: TranslateService,
    private translatePipe: TranslatePipe
  ) {
    this.initForm();
  }

  ngOnInit() {
    this.userActions = this._service.getUserActions();
    this.loadOptions();
    this.getRenderHeaders();

    this._route.url.pipe(takeUntil(this.destroy$)).subscribe((urlSegments: any) => {
      const url = urlSegments.map((segment: any) => segment.path).join('/');
      if (url.includes('new')) {
        this.pageMode = PageEditStatusEnum.add;

        this._route.queryParams.pipe(takeUntil(this.destroy$)).subscribe((params: any) => {
          if (params['id']) {
            this.salesId = +params['id'];
            this.loadDetail();
          }
        });
      } else if (url.includes('edit')) {
        this.pageMode = PageEditStatusEnum.edit;
        this._route.params.pipe(takeUntil(this.destroy$)).subscribe((params: any) => {
          if (params['id']) {
            this.salesId = +params['id'];
            this.loadDetail();
          }
        });
      } else {
        // 查看模式
        this.pageMode = PageEditStatusEnum.read;
        this._route.params.pipe(takeUntil(this.destroy$)).subscribe((params: any) => {
          if (params['id']) {
            this.salesId = +params['id'];
            this.loadDetail();
          }
        });
      }
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 加载选项数据
   */
  private loadOptions() {
    // 加载发票类型和开票方式选项
    this._service.getOrderListOptions().subscribe({
      next: (res) => {
        if (res.data) {
          this.invoiceTypeOptions = res.data.invoice_types || [];
          this.billingMethodOptions = res.data.billing_methods || [];
        }
      },
    });

    // 加载客户选项
    this._service.getBasic().subscribe({
      next: (res) => {
        if (res.data && res.data.list) {
          this.customerOptions = res.data.list;
          // 尝试填充客户名称
          this.tryFillNameFields();
        }
      },
    });

    // 加载组织架构选项
    this._service.getCustomer().subscribe({
      next: (res) => {
        if (res.data && res.data.children) {
          this.organizationOptions = res.data.children;
          // 如果已经有详情数据，重新填充表单以确保开票人下拉框正确显示
          if (this.detail && this.salesForm) {
            this.populateForm(this.detail);
          }
        }
      },
    });

    this._service
      .getCurrencyOptions({
        column: 'name',
        value: '',
        limit: 99999,
        page: 1,
        type: [9],
      })
      .subscribe({
        next: (res) => {
          if (res.data && res.data.option_list) {
            this.currencyOptions = res.data.option_list['9'] || [];
            // 尝试填充币种名称
            this.tryFillNameFields();
          }
        },
      });
  }

  private initForm() {
    // 直接创建表单，参考收款管理的方式
    this.salesForm = this._fb.group({
      id: [null],
      code: [{ value: null, disabled: false },[Validators.required]],
      invoice_type: [null,[Validators.required]],
      customer_id: [{ value: null }],
      customer_name: [null],
      receivable_date: [null, [Validators.required]],
      billing_date: [{ value: new Date(), disabled: false }, [Validators.required]],
      billing_method: [3],
      billing_user_id: [null, [Validators.required]],
      billing_user_name: [null],
      billing_dept_id: [null],
      billing_dept_name: [{ value: null, disabled: true }],
      exchange_rate: [{ value: null, disabled: false }, [Validators.required, Validators.min(0.0001)]],
      currency_id: [{ value: null }, [Validators.required]],
      currency_name: [null],
      tax_rate: [{ value: '13', disabled: false }, [Validators.required, Validators.min(0), Validators.max(100)]],
      remark: [null, [Validators.maxLength(50)]],
    });
    // 设置表单值变化监听
    this.setupFormValueChanges();
  }

  /**
   * 设置表单值变化监听
   */
  private setupFormValueChanges() {
    // 监听开票人变化，自动带出部门
    this.salesForm
      .get('billing_user_id')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((userId: any) => {
        if (userId && this.organizationOptions) {
          const department = this.findUserSecondLevelDepartment(this.organizationOptions, userId);

          if (!department.user_id) {
            // 有部门时，显示部门字段并赋值
            this.showDeptField = true;
            this.salesForm.patchValue({
              billing_dept_id: department.key,
              billing_dept_name: department.title,
            });
          } else {
            // 没有部门时，隐藏部门字段并清空数据
            this.showDeptField = false;
            this.salesForm.patchValue({
              billing_dept_id: null,
              billing_dept_name: null,
            });
          }
        } else {
          // 没有选择开票人时，隐藏部门字段并清空数据
          this.showDeptField = false;
          this.salesForm.patchValue({
            billing_dept_id: null,
            billing_dept_name: null,
          });
        }
      });

    // 监听客户变化，自动带出汇率
    this.salesForm
      .get('customer_id')
      ?.valueChanges.pipe(takeUntil(this.destroy$))
      .subscribe((customerId: any) => {
        if (customerId && this.customerOptions) {
          const customer = this.customerOptions.find((c: any) => c.id === customerId);
          if (customer && customer.rate) {
            this.salesForm.patchValue({
              exchange_rate: customer.rate,
            });
          }
        }
      });

    // 监听应收日期和开票日期变化，进行日期校验
    this.salesForm.get('receivable_date')?.valueChanges.subscribe(() => {
      this.validateDateRange();
    });

    this.salesForm.get('billing_date')?.valueChanges.subscribe(() => {
      this.validateDateRange();
    });
  }

  /**
   * 在组织架构中查找用户
   */
  private findUserInOrganization(nodes: any[], userId: number, parent?: any): any {
    for (const node of nodes) {
      if (node.user_id == userId) {
        return { ...node, parent };
      }
      if (node.children && node.children.length > 0) {
        const found = this.findUserInOrganization(node.children, userId, node);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 查找用户所在的第二层级部门
   * @param nodes 组织架构节点
   * @param userId 用户ID
   * @param level 当前层级（从1开始）
   * @param secondLevelDept 第二层级部门
   */
  private findUserSecondLevelDepartment(nodes: any[], userId: number, level = 1, secondLevelDept?: any): any {
    for (const node of nodes) {
      // 记录第二层级的部门
      const currentSecondLevel = level === 2 ? node : secondLevelDept;

      // 如果找到用户，返回第二层级部门
      if (node.user_id == userId) {
        return currentSecondLevel;
      }

      // 递归查找子节点
      if (node.children && node.children.length > 0) {
        const found = this.findUserSecondLevelDepartment(node.children, userId, level + 1, currentSecondLevel);
        if (found) return found;
      }
    }
    return null;
  }

  /**
   * 验证日期范围
   */
  private validateDateRange() {
    const receivableDate = this.salesForm.get('receivable_date')?.value;
    const billingDate = this.salesForm.get('billing_date')?.value;

    if (receivableDate && billingDate) {
      const receivableTime = new Date(receivableDate).setHours(0, 0, 0, 0);
      const billingTime = new Date(billingDate).setHours(0, 0, 0, 0);

      if (receivableTime < billingTime) {
        this.salesForm.get('billing_date')?.setErrors({
          dateRange: '开票日期不能大于应收日期',
        });
        this._message.error('开票日期不能大于应收日期');
      } else {
        // 清除日期范围错误，但保留其他错误
        const currentErrors = this.salesForm.get('billing_date')?.errors;
        if (currentErrors && currentErrors['dateRange']) {
          delete currentErrors['dateRange'];
          const hasOtherErrors = Object.keys(currentErrors).length > 0;
          this.salesForm.get('billing_date')?.setErrors(hasOtherErrors ? currentErrors : null);
        }
      }
    }
  }

  /**
   * 加载详情数据
   */
  private loadDetail() {
    const id = this.salesId || this._route.snapshot.params['id'];
    if (!id) return;

    this.loading = true;
    this._service.getDetail(id).subscribe({
      next: (res: { data: SalesDetailInterface }) => {
        if (res.data) {
          this.detail = res.data;
          this.orderStatus = res.data.status;
          this.populateForm(res.data);
          this.processDetailData();
        }
        this.loading = false;
      },
      error: () => {
        this.loading = false;
      },
    });
  }

  /**
   * 填充表单数据
   */
  private populateForm(data: SalesDetailInterface) {
    // 转换时间戳为Date对象
    const formData: any = {
      ...data,
      receivable_date: data.receivable_date ? new Date(data.receivable_date) : null,
      billing_date: data.billing_date ? new Date(data.billing_date) : null,
    };

    // 确保 billing_user_id 为字符串格式，以匹配 department-select 组件的 key 格式
    if (formData.billing_user_id) {
      formData.billing_user_id = String(formData.billing_user_id);
    }

    // 填充名称字段
    this.populateNameFields(data, formData);

    // 填充表单
    this.salesForm.patchValue(formData);

    // 初始化部门字段显示状态
    this.initDeptFieldVisibility(data);

    // 尝试填充名称字段（如果选项数据已加载）
    setTimeout(() => {
      this.tryFillNameFields();
    }, 0);
  }

  /**
   * 初始化部门字段显示状态
   */
  private initDeptFieldVisibility(data: SalesDetailInterface) {
    if (data.billing_user_id && this.organizationOptions.length > 0) {
      const department = this.findUserSecondLevelDepartment(this.organizationOptions, data.billing_user_id);
      this.showDeptField = !!department;
    } else {
      this.showDeptField = !!data.billing_dept_name; // 如果有部门名称则显示
    }
  }

  /**
   * 根据ID填充对应的名称字段（在初始表单填充时调用）
   */
  private populateNameFields(data: SalesDetailInterface, formData: any) {
    // 根据 currency_id 填充 currency_name（如果选项数据已加载）
    if (data.currency_id && this.currencyOptions.length > 0) {
      const currency = this.currencyOptions.find((option) => option.value === data.currency_id);
      if (currency) {
        formData.currency_name = currency.label;
      }
    }

    // 根据 customer_id 填充 customer_name（如果选项数据已加载）
    if (data.customer_id && this.customerOptions.length > 0) {
      const customer = this.customerOptions.find((option) => option.id === data.customer_id);
      if (customer) {
        formData.customer_name = customer.name;
      }
    }
  }

  /**
   * 尝试填充名称字段（无论何时调用都会检查条件并填充）
   */
  private tryFillNameFields() {
    if (!this.detail || !this.salesForm) {
      return;
    }
    const updates: any = {};

    // 根据 currency_id 填充 currency_name（如果选项数据已加载且当前表单中没有名称）
    if (this.detail.currency_id && this.currencyOptions.length > 0) {
      const currentCurrencyName = this.salesForm.get('currency_name')?.value;
      if (!currentCurrencyName) {
        const currency = this.currencyOptions.find((option) => option.value === this.detail.currency_id);
        if (currency) {
          updates.currency_name = currency.label;
        }
      }
    }

    // 根据 customer_id 填充 customer_name（如果选项数据已加载且当前表单中没有名称）
    if (this.detail.customer_id && this.customerOptions.length > 0) {
      const currentCustomerName = this.salesForm.get('customer_name')?.value;
      if (!currentCustomerName) {
        const customer = this.customerOptions.find((option) => option.id === this.detail.customer_id);
        if (customer) {
          updates.customer_name = customer.name;
        }
      }
    }

    // 如果有需要更新的字段，则更新表单
    if (Object.keys(updates).length > 0) {
      this.salesForm.patchValue(updates);
    }
  }

  /**
   * 处理明细数据，将嵌套结构扁平化
   */
  private processDetailData() {
    this.flatDetailList = [];

    if (this.detail.outbound_list && this.detail.outbound_list.length > 0) {
      this.detail.outbound_list.forEach((outbound, outboundIndex) => {
        if (outbound.detail_list && outbound.detail_list.length > 0) {
          outbound.detail_list.forEach((detail, detailIndex) => {
            // 将出库单、订单、交付单、应收单信息添加到明细项中
            const flatItem: SalesInvoiceDetailItem = {
              ...detail,
              outbound_id: outbound.outbound_id,
              outbound_code: outbound.outbound_code,
              order_code: outbound.order_code,
              order_uuid: outbound.order_uuid,
              po_code: outbound.po_code,
              po_unique_code: outbound.po_unique_code,
              bills_receivable_code: outbound.bills_receivable_code,
              bills_receivable_id: outbound.bills_receivable_id,
              style_code: outbound.style_code, // 添加款号
              category: outbound.category, // 添加品名
              // 添加索引信息用于跨行合并判断
              outbound_index: outboundIndex, // outbound_list中的索引
              detail_index: detailIndex, // detail_list中的索引
            };
            this.flatDetailList.push(flatItem);
          });
        }
      });
    }

    // 按outbound_index分组统计
    const outboundGroups = this.flatDetailList.reduce((groups, item) => {
      const key = `第${(item as any).outbound_index + 1}个出库单(${item.outbound_code})`;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
      return groups;
    }, {} as Record<string, any[]>);

    Object.keys(outboundGroups).forEach((key) => {});

    this.detailTableConfig.dataList = this.flatDetailList;
    this._cdr.detectChanges();
  }

  /**
   * 删除出库单
   */
  onDeleteOutbound(item: SalesInvoiceDetailItem) {
    // 计算该出库单包含的明细数量
    const outboundDetailCount = this.flatDetailList.filter((detail) => detail.outbound_code === item.outbound_code).length;

    this._modal.confirm({
      nzTitle: '确认删除出库单',
      nzContent: `确定要删除出库单 "${item.outbound_code}" 吗？<br/>
                  <span style="color: #ff4d4f;">此操作将删除该出库单下的所有 ${outboundDetailCount} 条明细数据，且不可恢复。</span>`,
      nzOkText: '确定删除',
      nzCancelText: '取消',
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => {
        this.deleteOutboundItem(item);
      },
    });
  }

  /**
   * 执行删除出库单操作
   */
  private deleteOutboundItem(item: SalesInvoiceDetailItem) {
    // 从原始数据中删除对应的出库单
    if (this.detail.outbound_list) {
      const outboundIndex = this.detail.outbound_list.findIndex((outbound) => outbound.outbound_code === item.outbound_code);

      if (outboundIndex > -1) {
        this.detail.outbound_list.splice(outboundIndex, 1);
        this.processDetailData(); // 重新处理数据
        this._message.success(`出库单 "${item.outbound_code}" 删除成功`);
      }
    }
  }

  /**
   * 返回列表
   */
  onBack() {
    this._router.navigate(['/settlement/sales-invoice-management']);
  }

  cancel() {
    this._modal
      .create({
        nzContent: SalesRefuseComponent,
        nzComponentParams: {
          title: '取消',
          flcErrorTip: '取消原因',
          placeholder: '请输入取消原因',
        },
        nzWidth: 400,
        nzClosable: false,
        nzWrapClassName: 'flc-confirm-modal',
        nzFooter: null,
      })
      .afterClose.subscribe((result: any) => {
        if (result?.success) {
          this._service.cancel({ id: Number(this.salesId), cancel_reason: result.reason }).subscribe((res) => {
            if (res.code === 200) {
              this._message.success('取消成功');
              this.loadDetail();
            }
          });
        }
      });
  }

  /**
   * 下拉框选择变化
   */
  onSelectChange(value: any, formItem: any) {
    // 处理下拉框选择变化的逻辑
  }

  /**
   * 树形选择器变化
   */
  onDapartmentChange(e: any) {
    this.salesForm.get('billing_user_name')?.setValue(e.title);
    // 根据选择的用户ID查找用户信息并设置部门
    // const user = this.findUserInOrganization(this.organizationOptions, e.key);
    // if (user && user.parent) {
    //   this.salesForm.patchValue({
    //     billing_dept_id: user.parent.key,
    //     billing_dept_name: user.parent.title,
    //   });
    // }
  }

  /**
   * 获取渲染表头
   */
  getRenderHeaders() {
    this.detailTableHeader = this._tableHelper.getRenderHeader<any>(this.headers);
  }

  /**
   * 表格列宽调整
   */
  onResize({ width }: NzResizeEvent, col: string): void {
    const version = '1.0.0';
    this.headers = this._tableHelper.tableResize<any>(width, col, this.headers, version, 'headers');
    this.getRenderHeaders();
  }

  /**
   * 表格设置按钮点击
   */
  changeHeader(event: MouseEvent): void {
    this.btnHighLight = true;
    const shadow = JSON.parse(JSON.stringify(this.headers));
    for (const item of shadow) {
      this._translate
        .get(item.label)
        .subscribe((res: string) => {
          item.label = res;
        })
        .unsubscribe();
    }
    this._tableHelper
      .openTableHeaderMidifyDialog<any>(shadow, event.target as HTMLElement)
      .pipe(finalize(() => (this.btnHighLight = false)))
      .subscribe((res) => {
        if (res) {
          for (const item of res) {
            item.label = this.headers.find((i) => i.key === item.key)?.label ?? '';
          }
          this.headers = res;
          this.getRenderHeaders();
        }
      });
  }

  /**
   * 判断是否应该显示操作按钮（只在每个出库单的第一行显示）
   */
  shouldShowActionButton(index: number): boolean {
    if (index === 0) return true;

    const currentItem = this.flatDetailList[index];
    const previousItem = this.flatDetailList[index - 1];

    // 如果当前行的出库单号与上一行不同，则显示操作按钮
    return currentItem.outbound_code !== previousItem.outbound_code;
  }

  /**
   * 获取相同出库单的行数（用于合并单元格）
   */
  getOutboundRowSpan(index: number): number {
    const currentItem = this.flatDetailList[index];
    if (!this.shouldShowActionButton(index)) {
      return 0; // 不是第一行，不显示
    }

    let count = 1;
    for (let i = index + 1; i < this.flatDetailList.length; i++) {
      if (this.flatDetailList[i].outbound_code === currentItem.outbound_code) {
        count++;
      } else {
        break;
      }
    }
    return count;
  }

  /**
   * 判断是否应该显示款号和品名（只在每个出库单的第一行显示）
   */
  shouldShowStyleAndCategory(index: number): boolean {
    if (index === 0) return true;

    const currentItem = this.flatDetailList[index] as any;
    const previousItem = this.flatDetailList[index - 1] as any;

    // 如果当前行的outbound_index与上一行不同，则显示款号和品名
    return currentItem.outbound_index !== previousItem.outbound_index;
  }

  /**
   * 获取相同出库单的行数（用于款号和品名的跨行合并）
   */
  getStyleCategoryRowSpan(index: number): number {
    const currentItem = this.flatDetailList[index] as any;
    if (!this.shouldShowStyleAndCategory(index)) {
      return 0; // 不是第一行，不显示
    }

    let count = 1;
    for (let i = index + 1; i < this.flatDetailList.length; i++) {
      if ((this.flatDetailList[i] as any).outbound_index === currentItem.outbound_index) {
        count++;
      } else {
        break;
      }
    }
    return count;
  }

  /**
   * 判断是否应该显示出库单号（只在每个出库单的第一行显示）
   */
  shouldShowOutboundCode(index: number): boolean {
    if (index === 0) return true;

    const currentItem = this.flatDetailList[index] as any;
    const previousItem = this.flatDetailList[index - 1] as any;

    // 如果当前行的outbound_index与上一行不同，则显示出库单号
    return currentItem.outbound_index !== previousItem.outbound_index;
  }

  /**
   * 获取相同出库单的行数（用于出库单号的跨行合并）
   */
  getOutboundCodeRowSpan(index: number): number {
    const currentItem = this.flatDetailList[index] as any;
    if (!this.shouldShowOutboundCode(index)) {
      return 0; // 不是第一行，不显示
    }

    let count = 1;
    for (let i = index + 1; i < this.flatDetailList.length; i++) {
      if ((this.flatDetailList[i] as any).outbound_index === currentItem.outbound_index) {
        count++;
      } else {
        break;
      }
    }
    return count;
  }

  /**
   * 判断是否应该显示订单号（只在每个订单的第一行显示）
   */
  shouldShowOrderCode(index: number): boolean {
    if (index === 0) return true;

    const currentItem = this.flatDetailList[index] as any;
    const previousItem = this.flatDetailList[index - 1] as any;

    // 如果当前行的outbound_index与上一行不同，则显示订单号
    // 因为我们按outbound_list的顺序显示，每个outbound_list项对应一个订单
    return currentItem.outbound_index !== previousItem.outbound_index;
  }

  /**
   * 获取相同订单号和出库单号的行数（用于订单号的跨行合并）
   */
  getOrderCodeRowSpan(index: number): number {
    const currentItem = this.flatDetailList[index] as any;
    if (!this.shouldShowOrderCode(index)) {
      return 0; // 不是第一行，不显示
    }

    let count = 1;
    for (let i = index + 1; i < this.flatDetailList.length; i++) {
      if ((this.flatDetailList[i] as any).outbound_index === currentItem.outbound_index) {
        count++;
      } else {
        break;
      }
    }
    return count;
  }

  /**
   * 获取序号（根据outbound_list的数量）
   */
  getRowNumber(index: number): number {
    if (!this.shouldShowOutboundCode(index)) {
      return 0; // 不是第一行，不显示序号
    }

    // 直接使用outbound_index + 1作为序号
    const currentItem = this.flatDetailList[index] as any;
    return currentItem.outbound_index + 1;
  }

  /**
   * 是否显示序号
   */
  shouldShowRowNumber(index: number): boolean {
    return this.shouldShowOutboundCode(index);
  }

  /**
   * 计算明细总计
   */
  get detailSummary() {
    if (!this.flatDetailList || this.flatDetailList.length === 0) {
      return {
        totalQty: 0,
        totalMoney: 0,
        totalReceivableMoney: 0,
        totalExcludeTaxMoney: 0,
        totalExcludeTaxLocal: 0,
        totalTaxAmount: 0,
        totalTaxAmountLocal: 0,
        totalReceivableMoneyLocal: 0,
      };
    }

    let totalQty = 0;
    let totalMoney = 0;
    let totalReceivableMoney = 0;
    let totalExcludeTaxMoney = 0;
    let totalExcludeTaxLocal = 0;
    let totalTaxAmount = 0;
    let totalTaxAmountLocal = 0;
    let totalReceivableMoneyLocal = 0;

    this.flatDetailList.forEach((item) => {
      totalQty = this._flcUtil.accAdd(totalQty, parseFloat(item.qty?.toString() || '0'));
      totalMoney = this._flcUtil.accAdd(totalMoney, parseFloat(item.total_money || '0'));
      totalReceivableMoney = this._flcUtil.accAdd(totalReceivableMoney, parseFloat(item.receivable_money || '0'));
      totalExcludeTaxMoney = this._flcUtil.accAdd(totalExcludeTaxMoney, parseFloat(item.exclude_tax_money || '0'));
      totalExcludeTaxLocal = this._flcUtil.accAdd(totalExcludeTaxLocal, parseFloat(item.exclude_tax_local || '0'));
      totalTaxAmount = this._flcUtil.accAdd(totalTaxAmount, parseFloat(item.tax_amount || '0'));
      totalTaxAmountLocal = this._flcUtil.accAdd(totalTaxAmountLocal, parseFloat(item.tax_amount_local || '0'));
      totalReceivableMoneyLocal = this._flcUtil.accAdd(totalReceivableMoneyLocal, parseFloat(item.receivable_money_local || '0'));
    });

    return {
      totalQty,
      totalMoney,
      totalReceivableMoney,
      totalExcludeTaxMoney,
      totalExcludeTaxLocal,
      totalTaxAmount,
      totalTaxAmountLocal,
      totalReceivableMoneyLocal,
    };
  }

  onSwitchToEdit() {
    this.pageMode = PageEditStatusEnum.edit;
  }

  /**
   * 提交或暂存销售发票
   * @param actionType 1-暂存 2-提交
   */
  onSubmit(actionType: number) {
    // 验证表单
    if (!this.validateForm()) {
      return;
    }

    // 验证明细数据
    if (!this.flatDetailList || this.flatDetailList.length === 0) {
      this._message.error('请添加发票明细数据');
      return;
    }

    this.saving = true;
    const isCommit = actionType === 2; // 2为提交，1为暂存
    const submitData = this.buildSubmitData(isCommit);

    this._service
      .submit(submitData)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.saving = false;
        })
      )
      .subscribe({
        next: (res: any) => {
          if (res.code === 200) {
            const successMsg = isCommit ? '提交成功' : '暂存成功';
            this._message.success(successMsg);

            // 如果是新建模式，更新ID并跳转
            if (res.data?.id) {
              this.salesId = res.data.id;
              this._router.navigate(['../../list', res.data.id], { relativeTo: this._route });
            }
            // 重新加载数据以获取最新状态
            this.loadDetail();

            // 如果是提交，切换到只读模式
            this.pageMode = PageEditStatusEnum.read;
          } else {
            this._message.error(res.message || '操作失败');
          }
        },
        error: (error: any) => {
          this._message.error('操作失败');
        },
      });
  }

  /**
   * 验证表单
   */
  private validateForm(): boolean {
    if (!this.salesForm.valid) {
      // 标记所有字段为已触摸，显示验证错误
      Object.keys(this.salesForm.controls).forEach((key) => {
        const control = this.salesForm.get(key);
        if (control) {
          control.markAsDirty();
          control.updateValueAndValidity();
        }
      });
      this._message.error('请检查必填项');
      return false;
    }
    return true;
  }

  /**
   * 构建提交数据
   */
  private buildSubmitData(isCommit: boolean): SalesCommitParams {
    const formValue = this.salesForm.getRawValue();
    // 转换日期为时间戳
    const receivableDate = formValue.receivable_date ? new Date(formValue.receivable_date).setHours(0, 0, 0, 0) : 0;
    const billingDate = formValue.billing_date ? new Date(formValue.billing_date).setHours(0, 0, 0, 0) : 0;

    // 构建出库单数据
    const outboundList: SalesCommitOutboundItem[] = [];

    this.flatDetailList.forEach((item) => {
      outboundList.push({
        outbound_id: item.outbound_id!,
        outbound_code: item.outbound_code || '',
        bills_receivable_id: item.bills_receivable_id!,
        bills_receivable_code: item.bills_receivable_code || '',
        order_uuid: item.order_uuid || '',
        po_unique_code: item.po_unique_code || '',
        sku_id: item.sku_id || 0,
        qty: item.qty?.toString() || '0',
        price: item.price || '0',
        total_money: item.total_money || '0',
        receivable_money: item.receivable_money || '0',
        receivable_money_local: item.receivable_money_local || '0',
        exclude_tax_price: item.exclude_tax_price || '0',
        exclude_tax_money: item.exclude_tax_money || '0',
        exclude_tax_local: item.exclude_tax_local || '0',
        tax_amount: item.tax_amount || '0',
        tax_amount_local: item.tax_amount_local || '0',
      });
    });

    const submitData: SalesCommitParams = {
      id: this.salesId!,
      code: formValue.code || '',
      invoice_type: formValue.invoice_type,
      customer_name: formValue.customer_name || '',
      customer_id: formValue.customer_id,
      receivable_date: receivableDate,
      billing_date: billingDate,
      billing_method: formValue.billing_method,
      billing_user_id: formValue.billing_user_id,
      billing_user_name: formValue.billing_user_name || '',
      billing_dept_id: formValue.billing_dept_id,
      billing_dept_name: formValue.billing_dept_name || '',
      exchange_rate: String(formValue.exchange_rate),
      currency_id: formValue.currency_id,
      currency_name: formValue.currency_name || '',
      tax_rate: String(formValue.tax_rate),
      remark: formValue.remark || '',
      is_commit: isCommit,
      version: this.detail.version,
      outbound_list: outboundList,
    };
    return submitData;
  }

  // 退回修改
  modify() {
    this._modal
      .create({
        nzContent: SalesRefuseComponent,
        nzComponentParams: {
          title: '退回修改',
          flcErrorTip: '退回修改原因',
          placeholder: '请输入退回原因',
        },
        nzWidth: 400,
        nzClosable: false,
        nzWrapClassName: 'flc-confirm-modal',
        nzFooter: null,
      })
      .afterClose.subscribe((result: any) => {
        if (result?.success) {
          this._service.batchReject([Number(this.salesId)], result.reason).subscribe((res) => {
            if (res.code === 200) {
              this._message.success('退回修改成功');
              this.loadDetail();
            }
          });
        }
      });
  }

  pass() {
    this._service.batchPass([String(this.salesId)]).subscribe((res) => {
      if (res.code === 200) {
        this._message.success(this.translatePipe.transform('success.pass'));
        this.loadDetail();
      }
    });
  }

  test() {
    // 测试方法
  }
}
