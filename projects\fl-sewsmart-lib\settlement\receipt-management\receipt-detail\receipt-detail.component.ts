import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil, finalize } from 'rxjs';
import {
  FlcDrawerHelperService,
  FlcTableComponent,
  FlcValidatorService,
  FlcUtilService,
  FlcTableHelperService,
  resizable,
} from 'fl-common-lib';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzResizeEvent } from 'ng-zorro-antd/resizable';
import { RecerptService } from '../receipt.service';
import {
  ReceiptDetail,
  AdvanceReceiptDetail,
  ReceivableReceiptDetail,
  V1BillsReceivableDetailOutbound,
  CustomerOption,
  AccountOption,
  PageEditStatusEnum,
  ReceiptTypeEnum,
  OrderStatus,
  AuditActionType,
} from '../models/receipt-detail.interface';
import { receiptTypeOptions, currencyOptions, advanceDetailTableHeader, receivableDetailTableHeader, formConfig } from '../models/config';
import { OrderSelectorDrawerComponent } from '../components/order-selector-drawer/order-selector-drawer.component';
import { ReceivableSelectorDrawerComponent } from '../components/receivable-selector-drawer/receivable-selector-drawer.component';
import { ReceiptRefuseComponent } from '../components/refuse-modal/refuse-modal.component';

const version = '1.4.5';
@Component({
  selector: 'flss-receipt-detail',
  templateUrl: './receipt-detail.component.html',
  styleUrls: ['./receipt-detail.component.scss'],
  providers: [TranslatePipe],
})
@resizable()
export class ReceiptDetailComponent implements OnInit, OnDestroy {
  @ViewChild('advanceTable', { static: false }) advanceTable!: FlcTableComponent;
  @ViewChild('receivableTable', { static: false }) receivableTable!: FlcTableComponent;

  private destroy$ = new Subject<void>();
  PageEditStatusEnum = PageEditStatusEnum;
  translateName = 'receiptManagement.';
  formConfig = formConfig;
  receiptForm!: FormGroup;
  receiptId?: number;
  loading = false;
  saving = false;
  // 页面模式
  orderStatus: OrderStatus = 1; // 当前订单状态
  OrderStatus = OrderStatus;
  pageMode = PageEditStatusEnum.read;
  detail: any = {};
  userActions: string[] = [];
  checked = false;
  // 选项数据
  receiptTypeOptions = receiptTypeOptions;
  currencyOptions: any = [];
  customerOptions: CustomerOption[] = [];
  accountOptions: AccountOption[] = [];

  // 表格配置
  advanceTableHeader = advanceDetailTableHeader;
  receivableTableHeader = receivableDetailTableHeader;

  headers: any[] = advanceDetailTableHeader;
  headers2: any[] = receivableDetailTableHeader;
  advanceTableConfig = {
    tableName: 'advanceDetailTable',
    dataList: <AdvanceReceiptDetail[]>[],
    count: 0,
    height: 400,
    loading: false,
    pageSize: 20,
    pageIndex: 1,
    detailBtn: false,
    canSetHeader: true,
    actionWidth: '80px',
    hasCheckbox: false,
    hasAction: true,
    uniqueId: 'id',
    width: '100%',
  };
  btnHighLight = false;
  receivableTableConfig = {
    tableName: 'receivableDetailTable',
    dataList: <ReceivableReceiptDetail[]>[],
    count: 0,
    height: 400,
    loading: false,
    pageSize: 20,
    pageIndex: 1,
    translateName: '',
    detailBtn: false,
    canSetHeader: true,
    actionWidth: '80px',
    hasCheckbox: false,
    settingBtnPos: 'start',
    trCheck: false,
    hasAction: true,
    uniqueId: 'id',
    width: '100%',
  };

  preReceiptAmount: { [key: string]: any } = {};
  canUsedAmount: { [key: string]: any } = {};
  originalCanUsedAmount: { [key: string]: any } = {}; // 原始可占用金额副本

  actionMap = {
    hasCancel: false,        // 取消权限
    hasConfirm: false,       // 确认权限
    hasAuditPass: false,     // 审核通过权限
    hasDelete: false,        // 删除权限
    hasCreate: false,        // 创建权限
    hasUpdate: false,        // 更新权限
    hasAuditReturn: false,   // 审核退回权限
  }
  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private receiptService: RecerptService,
    private message: NzMessageService,
    private drawer: NzDrawerService,
    private drawerHelper: FlcDrawerHelperService,
    private validator: FlcValidatorService,
    private modalService: NzModalService,
    private translatePipe: TranslatePipe,
    private _flcUtil: FlcUtilService,
    private _tableHelper: FlcTableHelperService,
    private _translate: TranslateService
  ) {
    this.initForm();
  }

  ngOnInit() {
    this.userActions = this.receiptService.getUserActions();
    this.setActionPermissions();
    this.route.url.pipe(takeUntil(this.destroy$)).subscribe((urlSegments: any) => {
      const url = urlSegments.map((segment: any) => segment.path).join('/');
      if (url.includes('new')) {
        this.pageMode = PageEditStatusEnum.add;

        this.route.queryParams.pipe(takeUntil(this.destroy$)).subscribe((params: any) => {
          if (params['id']) {
            this.receiptId = +params['id'];
            this.loadReceiptDetail();
          } else {
            this.generateReceiptCode();
          }
        });
      } else if (url.includes('edit')) {
        this.pageMode = PageEditStatusEnum.edit;
      } else {
        // 查看模式
        this.pageMode = PageEditStatusEnum.read;
        this.route.params.pipe(takeUntil(this.destroy$)).subscribe((params: any) => {
          if (params['id']) {
            this.receiptId = +params['id'];
            this.loadReceiptDetail();
          }
        });
      }
    });

    this.loadBasicData();
    this.setupFormSubscriptions();
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initForm() {
    this.receiptForm = this.fb.group({
      id: [null],
      code: [{ value: '' }, [Validators.required, Validators.maxLength(20)]], // 修复：添加code控件
      type: [1, [Validators.required]], // 修复：添加type控件
      customer_id: [null, [Validators.required]],
      customer_name: [null, [Validators.required]],
      account_id: [null, [Validators.required]],
      receipt_date: [new Date().setHours(0, 0, 0, 0), [Validators.required]],
      currency_name: [null],
      currency_id: [null, [Validators.required]], // 修复：添加currency_id控件
      exchange_rate: [{ value: 1, disabled: false }, [Validators.required, Validators.min(0.0001)]],
      remark: [null, [Validators.maxLength(50)]],
    });
  }

  private setupFormSubscriptions() {
    // // 监听客户变化
    // this.receiptForm
    //   .get('customer_id')
    //   ?.valueChanges.pipe(takeUntil(this.destroy$))
    //   .subscribe((customerId: string) => {
    //     if (customerId) {
    //       this.onCustomerChange(customerId);
    //     }
    //   });

    // // 监听币种变化
    // this.receiptForm
    //   .get('currency_id')
    //   ?.valueChanges.pipe(takeUntil(this.destroy$))
    //   .subscribe((currencyId: string) => {
    //     this.onCurrencyChange(currencyId);
    //   });
  }

  private loadBasicData() {
    // 加载客户选项
    this.receiptService
      .getCustomerOptions()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res.code === 200) {
            this.customerOptions = res.data.list || [];
            // customer_name有值时自动赋值customer_id
            const customerName = this.receiptForm.get('customer_name')?.value;
            if (customerName) {
              const found = this.customerOptions.find((e: any) => e.short_name === customerName || e.name === customerName);
              if (found) {
                this.receiptForm.patchValue({ customer_id: found.id });
              }
            }
          }
        },
        error: (error: any) => {
          console.error('获取客户选项失败:', error);
        },
      });

    // 加载账户选项
    this.receiptService
      .getAccountOptions()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res.code === 200) {
            this.accountOptions = res.data.list || [];
          }
        },
        error: (error: any) => {
          console.error('获取账户选项失败:', error);
        },
      });

    // 加载币种选项
    this.receiptService
      .getCurrencyOptions({
        column: 'name',
        value: '',
        limit: 99999,
        page: 1,
        type: [9],
      })
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res.code === 200) {
            this.currencyOptions = res.data.option_list['9'] || [];
          }
        },
        error: (error: any) => {
          console.error('获取币种选项失败:', error);
        },
      });
  }

  private generateReceiptCode() {
    this.receiptService
      .generateReceiptCode()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res.code === 200) {
            this.receiptForm.patchValue({
              code: res.data.code, // 同时更新code字段
            });
          }
        },
        error: (error: any) => {
          console.error('生成收款单号失败:', error);
          this.message.error('生成收款单号失败');
        },
      });
  }

  private loadReceiptDetail() {
    if (this.receiptId == undefined || this.receiptId == null) return;

    this.loading = true;
    this.receiptService
      .getDetail(this.receiptId?.toString() || '')
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res.code === 200) {
            this.populateForm(res.data);
          } else {
            this.message.error(res.message || '获取收款单详情失败');
          }
          this.loading = false;
        },
        error: (error: any) => {
          console.error('获取收款单详情失败:', error);
          this.message.error('获取收款单详情失败');
          this.loading = false;
        },
      });
  }

  /**
   * 根据接口数据填充表单和明细列表
   * @param data 从API获取的收款单详情数据
   */
  private populateForm(data: ReceiptDetail) {
    // 设置订单状态
    this.orderStatus = data?.status;

    // 设置详情对象
    this.detail = {
      ...data,
      remark: data.remake || data.remark, // 兼容不同的字段名
      status_label: this.getStatusLabel(data.status),
    };

    // 填充基础表单数据
    this.populateBasicForm(data);

    // 根据收款类型填充明细数据
    this.populateDetailLists(data);

    // 设置表单状态
    this.setFormState();
  }

  /**
   * 填充基础表单字段
   */
  private populateBasicForm(data: ReceiptDetail) {
    this.receiptForm.patchValue({
      id: data.id || null,
      code: data.code || '',
      type: data.type || '1',
      customer_id: data.customer_id || '',
      customer_name: data.customer_name || '',
      account_id: data.account_id || '',
      receipt_date: data.receipt_date && data.receipt_date > 0 ? new Date(data.receipt_date) : 0,
      currency_id: data.currency_id || '1',
      currency_name: data.currency_name || '人民币',
      exchange_rate: data.exchange_rate || '1',
      remark: data.remake || data.remark || '',
    });

    // customer_name有值时自动赋值customer_id
    const customerName = data.customer_name || '';
    if (customerName && this.customerOptions.length > 0) {
      const found = this.customerOptions.find((e: any) => e.short_name === customerName || e.name === customerName);
      if (found) {
        this.receiptForm.patchValue({ customer_id: found.id });
      }
    }
  }
  /**
   * 根据收款类型填充明细列表数据
   */
  private populateDetailLists(data: ReceiptDetail) {
    // 清空现有数据
    this.clearDetailLists();

    const typeStr = data.type;
    if (typeStr === ReceiptTypeEnum.advance) {
      // 预收款类型 - 填充预收款明细
      this.populateAdvanceDetails(data.pre_payment_list || []);
    } else if (typeStr === ReceiptTypeEnum.receivable) {
      // 应收款类型 - 填充应收款明细
      this.populateReceivableDetails(data.payment_list || []);
    }
  }

  /**
   * 填充应收款明细数据 - 处理嵌套的 Settlementv1BillsReceivableDetail 格式
   */
  private populateReceivableDetails(paymentList: any[]) {
    if (!paymentList || paymentList.length === 0) {
      return;
    }

    // 复用现有的数据处理方法
    const convertedData = this.processReceivableData(paymentList);

    // 设置到表格配置
    this.receivableTableConfig.dataList = convertedData;
    this.receivableTableConfig.count = convertedData.length;
    this.receivableTableConfig.hasAction = this.pageMode !== PageEditStatusEnum.read;
    this.receivableTableConfig = { ...this.receivableTableConfig };
  }

  /**
   * 填充预收款明细数据
   */
  private populateAdvanceDetails(prePaymentList: AdvanceReceiptDetail[]) {
    if (!prePaymentList || prePaymentList.length === 0) {
      return;
    }

    // 验证和转换数据格式
    const validatedData = prePaymentList.map((item) => this.validateAdvanceDetail(item));

    // 设置到表格配置
    this.advanceTableConfig.dataList = validatedData;
    this.advanceTableConfig.count = validatedData.length;
    this.advanceTableConfig.hasAction = this.pageMode !== PageEditStatusEnum.read;
    this.advanceTableConfig = { ...this.advanceTableConfig };
  }

  /**
   * 验证和标准化预收款明细数据
   */
  private validateAdvanceDetail(item: any): AdvanceReceiptDetail {
    return {
      order_uuid: item.order_uuid || '',
      order_code: item.order_code || '',
      order_qty: item.order_qty || item.order_qyt || '0',
      style_code: item.style_code || '',
      category: item.category || '',
      currency_name: item.currency_name || item.currency_type || '人民币',
      estimated_amount: item.estimated_amount || '0',
      pre_receiving_amount: item.pre_receiving_amount || item.prepayment || '0',
      pre_receiving_amount_local: item.pre_receiving_amount_local || item.prepayment_local || '0',
      received_amount: item.received_amount || '0',
      received_amount_local: item.received_amount_local || '0',
      payment: item.payment || item.current_receipt_amount || '0',
      payment_local: item.payment_local || item.current_receipt_amount_local || '0',
      po_codes: item.po_codes || item.delivery_codes || [],
      ratio: item.ratio,
    };
  }

  /**
   * 清空明细列表数据
   */
  private clearDetailLists() {
    this.advanceTableConfig.dataList = [];
    this.advanceTableConfig.count = 0;
    this.receivableTableConfig.dataList = [];
    this.receivableTableConfig.count = 0;
  }

  /**
   * 设置表单状态
   */
  private setFormState() {
    // 更新表格配置
    this.advanceTableConfig = { ...this.advanceTableConfig };
    this.receivableTableConfig = { ...this.receivableTableConfig };
  }

  private getStatusLabel(status: number): string {
    const statusMap: { [key: number]: string } = {
      1: '待提交',
      2: '待审核',
      3: '待修改',
      4: '待收款',
      5: '已收款',
      6: '已取消',
      8: '已取消',
      9: '修改待审核',
      10: '修改未通过',
    };
    return statusMap[status] || '未知状态';
  }

  /* ==================== 表单事件处理 ==================== */

  poSelectChange(val: any, item: any) {
    if (item.key == 'type') {
      this.onReceiptTypeChange();
    }
    if (item.key == 'customer_id') {
      this.receiptForm.get('customer_name')?.setValue(this.customerOptions.find((e: any) => e.id === val)?.short_name);
    }
  }

  onReceiptTypeChange() {
    // 清空明细数据
    this.advanceTableConfig.dataList = [];
    this.advanceTableConfig.count = 0;
    this.receivableTableConfig.dataList = [];
    this.receivableTableConfig.count = 0;

    // 强制刷新表格以正确显示空状态
    this.advanceTableConfig = { ...this.advanceTableConfig };
    this.receivableTableConfig = { ...this.receivableTableConfig };
  }

  onCustomerChange(customerId: string) {
    return;
    // 获取客户汇率信息
    // this.receiptService
    //   .getCustomerExchangeRate(customerId)
    //   .pipe(takeUntil(this.destroy$))
    //   .subscribe({
    //     next: (res: any) => {
    //       if (res.code === 200) {
    //         // 使用currency_name字段，这是API实际返回的字段
    //         const currencyValue = res.data.currency_name || 'CNY';
    //         const currencyId = res.data.currency_id || '1';
    //         this.receiptForm.patchValue({
    //           currency: currencyValue,
    //           currency_id: currencyId, // 同时更新currency_id
    //           exchange_rate: res.data.exchange_rate,
    //         });

    //         // 根据币种设置汇率是否可编辑
    //         this.onCurrencyChange(currencyValue);
    //       }
    //     },
    //     error: (error: any) => {
    //       console.error('获取客户汇率失败:', error);
    //     },
    //   });
  }

  onCurrencyChange(currency: string) {
    if (this.currencyOptions.find((item: any) => item.value === currency)?.label === '元') {
      currency = 'CNY';
    }
    const exchangeRateControl = this.receiptForm.get('exchange_rate');
    if (currency === 'CNY') {
      // 人民币汇率固定为1且不可修改
      exchangeRateControl?.setValue(1);
      exchangeRateControl?.disable();
    } else {
      exchangeRateControl?.setValue('', { emitViewToModelChange: false })
      // 非人民币需要手动维护汇率
      exchangeRateControl?.enable();
    }
  }

  /**
   * 处理汇率变化 - 重新计算所有本币金额
   */
  onExchangeRateChange(exchangeRate: number) {
    // 重新计算预收单明细的本币金额
    this.recalculateAdvanceLocalAmounts(exchangeRate);

    // 重新计算应收单明细的本币金额
    this.recalculateReceivableLocalAmounts(exchangeRate);
  }

  /**
   * 重新计算预收单明细的本币金额
   */
  private recalculateAdvanceLocalAmounts(exchangeRate: number) {
    const advanceData = this.advanceTableConfig.dataList as AdvanceReceiptDetail[];
    advanceData.forEach((item) => {
      if (item.payment && parseFloat(String(item.payment)) > 0) {
        item.payment_local = this.calculateExchangeRatePayment(String(item.payment), 2, exchangeRate);
      }
    });

    // 刷新表格
    this.advanceTableConfig = { ...this.advanceTableConfig };
  }

  /**
   * 重新计算应收单明细的本币金额
   */
  private recalculateReceivableLocalAmounts(exchangeRate: number) {
    const receivableData = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];
    receivableData.forEach((item) => {
      if (item.current_receipt_amount && parseFloat(item.current_receipt_amount) > 0) {
        item.current_receipt_amount_local = this.calculateExchangeRatePayment(item.current_receipt_amount, 2, exchangeRate);
      }
    });

    // 刷新表格
    this.receivableTableConfig = { ...this.receivableTableConfig };
  }

  /* ==================== 明细操作 ==================== */
  changeHeader(event: MouseEvent, type: number): void {
    this.btnHighLight = true;
    const shadow = JSON.parse(JSON.stringify(type === 1 ? this.headers : this.headers2));

    for (const item of shadow) {
      this._translate
        .get(item.label)
        .subscribe((res: string) => {
          item.label = res;
        })
        .unsubscribe();
    }
    this._tableHelper
      .openTableHeaderMidifyDialog<any>(shadow, event.target as HTMLElement, 'start')
      .pipe(finalize(() => (this.btnHighLight = false)))
      .subscribe((res) => {
        if (res) {
          for (const item of res) {
            item.label = (type === 1 ? this.headers : this.headers2).find((i) => i.key === item.key)?.label ?? '';
          }
          if (type === 1) {
            this.headers = res;
            this.getRenderHeaders();
            // this._tableHelper.saveFlcTableHeaderConfig(this.headers, version, 'headers');
          } else {
            this.headers2 = res;
            this.getRenderHeaders2();
            // this._tableHelper.saveFlcTableHeaderConfig(this.headers2, version, 'headers2');
          }
        }
      });
  }
  getRenderHeaders() {
    this.advanceTableHeader = this._tableHelper.getRenderHeader<any>(this.headers);
  }
  getRenderHeaders2() {
    this.receivableTableHeader = this._tableHelper.getRenderHeader<any>(this.headers2);
  }
  onResize({ width }: NzResizeEvent, col: string, type: number) {
    // this.headers = this._tableHelper.tableResize<any>(width, col, this.headers, version, 'headers');
    // this.getRenderHeaders();
    this.headers = this._tableHelper.tableResize<any>(width, col, this.headers, version, 'headers');
    this.headers2 = this._tableHelper.tableResize<any>(width, col, this.headers2, version, 'headers2');
    type === 1 ? this.getRenderHeaders() : this.getRenderHeaders2();
  }

  onAddAdvanceDetail() {
    const customerId = this.receiptForm.get('customer_name')?.value;
    const currency = this.receiptForm.get('currency_id')?.value;

    // 打开订单选择抽屉
    const drawerRef = this.drawer.create({
      nzTitle: '选择订单需求',
      nzContent: OrderSelectorDrawerComponent,
      nzContentParams: {
        customerName: customerId,
        currency: currency,
        historySelectedIds: this.advanceTableConfig.dataList.map((item: AdvanceReceiptDetail) => item.order_code),
      },
      nzPlacement: 'bottom',
      nzHeight: '80%',
      nzMaskClosable: false,
      nzBodyStyle: { padding: '0' },
    });

    drawerRef.afterClose.pipe(takeUntil(this.destroy$)).subscribe((result: any) => {
      if (result && result.selectedItems) {
        this.addAdvanceDetails(result.selectedItems);
      }
    });
  }

  onAddReceivableDetail() {
    const customerId = this.receiptForm.get('customer_id')?.value;
    const currency = this.receiptForm.get('currency_id')?.value;

    // 打开应收单选择抽屉
    const drawerRef = this.drawer.create({
      nzTitle: '选择应收单',
      nzContent: ReceivableSelectorDrawerComponent,
      nzContentParams: {
        customerName: customerId,
        currency: currency,
        historySelectedIds: this.receivableTableConfig.dataList.map((item: ReceivableReceiptDetail) => item.bills_receivable_code),
      },
      nzPlacement: 'bottom',
      nzHeight: '80%',
      nzMaskClosable: false,
      nzBodyStyle: { padding: '0' },
    });

    drawerRef.afterClose.pipe(takeUntil(this.destroy$)).subscribe((result: any) => {
      if (result && result.selectedItems) {
        this.handleReceivableSelected(result.selectedItems);
      }
    });
  }

  private addAdvanceDetails(selectedOrders: any[]) {
    let arr: AdvanceReceiptDetail[] = JSON.parse(JSON.stringify(this.advanceTableConfig.dataList));
    selectedOrders.forEach((order) => {
      if (arr.some((a) => a.order_code == order.order_code)) return;

      const detail: AdvanceReceiptDetail = {
        ...order,
        currency_name: order.currency_type,
        pre_receiving_amount: order.prepayment,
        pre_receiving_amount_local: order.prepayment_local,
        received_amount: order?.payment ?? 0,
        received_amount_local: order?.payment_local ?? 0,
        payment: 0,
        payment_local: 0,
        po_codes: order.po_codes || [],
        ratio: 0,
      };

      arr.push(detail);
    });

    this.receiptForm?.get('customer_id')?.setValue(this.customerOptions.find((e: any) => e.short_name === arr[0].customer_name)?.id, { emitViewToModelChange: false })
    this.receiptForm?.get('customer_name')?.setValue(arr[0].customer_name, { emitViewToModelChange: false })
    this.receiptForm?.get('currency_id')?.setValue(arr[0].currency_id, { emitViewToModelChange: false })
    this.receiptForm?.get('currency_name')?.setValue(arr[0].currency_type, { emitViewToModelChange: false })
    this.onCurrencyChange(arr[0].currency_id)
    // this.receiptForm.patchValue({
    //   customer_id: this.customerOptions.find((e: any) => e.short_name === arr[0].customer_name)?.id,
    //   customer_name: arr[0].customer_name,
    //   currency_id: arr[0].currency_id,
    //   currency_name: arr[0].currency_type,
    // });

    this.advanceTableConfig = { ...this.advanceTableConfig, dataList: arr, count: arr.length };
  }

  private handleReceivableSelected(selectedReceivables: any) {
    if (!selectedReceivables || selectedReceivables.length === 0) {
      return;
    }

    this.receiptForm.patchValue({
      customer_id: selectedReceivables[0].customer_id,
      customer_name: selectedReceivables[0].customer_name,
      currency_id: this.currencyOptions.find((e: any) => e.label === selectedReceivables[0].currency_name)?.value || 17,
      currency_name: selectedReceivables[0].currency_name,
    });
    this.receiptService
      .getReceivableDetail({
        ids: selectedReceivables.map((s: any) => s.bills_receivable_id),
      })
      .subscribe((res: any) => {
        if (res.code === 200 && res.data) {
          const processedData = this.processReceivableData(res.data.list);
          this.mergeReceivableData(processedData);
          this.updateReceivableTable();
        } else {
          this.message.error(res.message || '获取应收单详情失败');
        }
      });
  }

  /**
   * 处理应收单数据 - 按应收单-出库单-订单-交付单维度展示
   */
  private processReceivableData(apiData: any[]): ReceivableReceiptDetail[] {
    const detailData: ReceivableReceiptDetail[] = [];

    apiData.forEach((receivableItem) => {
      // 遍历出库单列表
      if (receivableItem.outbound_list && Array.isArray(receivableItem.outbound_list)) {
        receivableItem.outbound_list.forEach((outbound: any) => {
          // 遍历订单列表
          if (outbound.order_list && Array.isArray(outbound.order_list)) {
            outbound.order_list.forEach((order: any) => {
              // 遍历交付单列表，每个交付单作为一行
              if (order.po_list && Array.isArray(order.po_list)) {
                order.po_list.forEach((po: any) => {
                  const detail = this.createReceivableDetailRow(receivableItem, outbound, order, po);
                  if (detail) {
                    detailData.push(detail);
                  }
                });
              }
            });
          }
        });
      }
    });

    // 按维度排序数据
    const sortedData = this.sortReceivableData(detailData);

    // 初始化预收款数据
    this.initializeAdvancePaymentData(sortedData);

    return sortedData;
  }

  /**
   * 创建单个应收明细行数据 - 每个交付单作为一行
   */
  private createReceivableDetailRow(receivableItem: any, outbound: any, order: any, po: any): ReceivableReceiptDetail | null {
    if (!po) {
      return null;
    }

    // 创建明细记录
    const detail: ReceivableReceiptDetail = {
      // 基础标识信息
      bills_receivable_code: receivableItem.bills_receivable_code || '',
      bills_receivable_id: receivableItem.bills_receivable_id || '',
      outbound_code: outbound.outbound_code || '',
      outbound_id: outbound.outbound_id || '',
      order_code: order.order_code || '',
      order_uuid: order.order_uuid || '',
      po_code: po.po_code || '',
      po_unique_code: po.po_unique_code || '',

      // 业务字段
      style_code: po.style_code || '',
      category: po.category || '',
      delivery_codes: po.po_code ? [po.po_code] : [],
      quantity: (po.qty || '0').toString(),

      // 金额字段
      receivable_amount: (po.expected_amount || '0').toString(),
      received_amount: (po.received_amount || '0').toString(),
      pending_amount: (po.pending_amount || '0').toString(),
      current_receipt_amount: (po.current_receipt_amount || '0').toString(),
      current_receipt_amount_local: (po.current_receipt_amount_local || '0').toString(),
      used_advance_amount: (po.prepayment_occupied_amount || '0').toString(),

      // 预收款相关字段
      advance_amount: (po.pre_payment || '0').toString(),
      actual_available_advance: '0', // 将在初始化时计算

      // 其他字段
      id: po.id || '',
      mark: false,
      currentMark: false,
    };

    return detail;
  }

  /**
   * 按维度排序数据 - 确保相同维度的数据相邻显示
   */
  private sortReceivableData(detailData: ReceivableReceiptDetail[]): ReceivableReceiptDetail[] {
    return detailData.sort((a, b) => {
      // 首先按应收单号排序
      if (a.bills_receivable_code !== b.bills_receivable_code) {
        return (a.bills_receivable_code || '').localeCompare(b.bills_receivable_code || '');
      }

      // 然后按出库单号排序
      if (a.outbound_code !== b.outbound_code) {
        return (a.outbound_code || '').localeCompare(b.outbound_code || '');
      }

      // 再按订单号排序
      if (a.order_code !== b.order_code) {
        return (a.order_code || '').localeCompare(b.order_code || '');
      }

      // 最后按交付单号排序
      return (a.po_code || '').localeCompare(b.po_code || '');
    });
  }

  /**
   * 初始化预收款数据 - 按订单汇总预收款信息
   */
  private initializeAdvancePaymentData(detailData: ReceivableReceiptDetail[]) {
    // 按订单分组统计预收款数据
    const orderAdvanceMap = new Map<string, { totalPrePayment: number; totalUsedAdvance: number }>();

    detailData.forEach((detail) => {
      const orderUuid = detail.order_uuid;
      if (!orderUuid) return;

      const prePayment = parseFloat(detail.advance_amount || '0');
      const usedAdvance = parseFloat(detail.used_advance_amount || '0');

      if (orderAdvanceMap.has(orderUuid)) {
        const existing = orderAdvanceMap.get(orderUuid)!;
        existing.totalPrePayment += prePayment;
        existing.totalUsedAdvance += usedAdvance;
      } else {
        orderAdvanceMap.set(orderUuid, {
          totalPrePayment: prePayment,
          totalUsedAdvance: usedAdvance,
        });
      }
    });

    // 设置预收款相关数据
    orderAdvanceMap.forEach((advanceInfo, orderUuid) => {
      this.preReceiptAmount[orderUuid] = advanceInfo.totalPrePayment;
      this.canUsedAmount[orderUuid] = Math.max(0, advanceInfo.totalPrePayment - advanceInfo.totalUsedAdvance);
      this.originalCanUsedAmount[orderUuid] = Math.max(0, advanceInfo.totalPrePayment - advanceInfo.totalUsedAdvance);
    });

    // 更新每行的实际可占用预收款
    detailData.forEach((detail) => {
      if (detail.order_uuid) {
        detail.actual_available_advance = (this.canUsedAmount[detail.order_uuid] || 0).toString();
      }
    });
  }

  /**
   * 判断是否显示应收单号 - 相同应收单号时只在第一行显示
   */
  shouldShowReceivableCode(index: number): boolean {
    const dataList = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];
    if (index === 0) return true;

    const currentItem = dataList[index];
    const prevItem = dataList[index - 1];

    return currentItem.bills_receivable_code !== prevItem.bills_receivable_code;
  }

  /**
   * 判断是否显示出库单号 - 相同出库单号时只在第一行显示
   */
  shouldShowOutboundCode(index: number): boolean {
    const dataList = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];
    if (index === 0) return true;

    const currentItem = dataList[index];
    const prevItem = dataList[index - 1];

    return currentItem.bills_receivable_code !== prevItem.bills_receivable_code ||
           currentItem.outbound_code !== prevItem.outbound_code;
  }

  /**
   * 判断是否显示订单号 - 相同订单号时只在第一行显示
   */
  shouldShowOrderCode(index: number): boolean {
    const dataList = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];
    if (index === 0) return true;

    const currentItem = dataList[index];
    const prevItem = dataList[index - 1];

    return currentItem.bills_receivable_code !== prevItem.bills_receivable_code ||
           currentItem.outbound_code !== prevItem.outbound_code ||
           currentItem.order_code !== prevItem.order_code;
  }

  /**
   * 计算应收单号的rowspan
   */
  getReceivableCodeRowSpan(index: number): number {
    const dataList = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];
    if (!this.shouldShowReceivableCode(index)) return 0;

    const currentItem = dataList[index];
    let count = 1;

    // 向后查找相同应收单号的行数
    for (let i = index + 1; i < dataList.length; i++) {
      if (dataList[i].bills_receivable_code === currentItem.bills_receivable_code) {
        count++;
      } else {
        break;
      }
    }

    return count;
  }

  /**
   * 计算出库单号的rowspan
   */
  getOutboundCodeRowSpan(index: number): number {
    const dataList = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];
    if (!this.shouldShowOutboundCode(index)) return 0;

    const currentItem = dataList[index];
    let count = 1;

    // 向后查找相同应收单号和出库单号的行数
    for (let i = index + 1; i < dataList.length; i++) {
      if (dataList[i].bills_receivable_code === currentItem.bills_receivable_code &&
          dataList[i].outbound_code === currentItem.outbound_code) {
        count++;
      } else {
        break;
      }
    }

    return count;
  }

  /**
   * 计算订单号的rowspan
   */
  getOrderCodeRowSpan(index: number): number {
    const dataList = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];
    if (!this.shouldShowOrderCode(index)) return 0;

    const currentItem = dataList[index];
    let count = 1;

    // 向后查找相同应收单号、出库单号和订单号的行数
    for (let i = index + 1; i < dataList.length; i++) {
      if (dataList[i].bills_receivable_code === currentItem.bills_receivable_code &&
          dataList[i].outbound_code === currentItem.outbound_code &&
          dataList[i].order_code === currentItem.order_code) {
        count++;
      } else {
        break;
      }
    }

    return count;
  }

  /**
   * 根据用户权限设置actionMap
   */
  private setActionPermissions(): void {
    this.actionMap = {
      hasCancel: this.userActions.includes('settlement:receipt-management-cancel'),
      hasConfirm: this.userActions.includes('settlement:receipt-management-confirm'),
      hasAuditPass: this.userActions.includes('settlement:receipt-management-audit-pass'),
      hasDelete: this.userActions.includes('settlement:receipt-management-delete'),
      hasCreate: this.userActions.includes('settlement:receipt-management-create'),
      hasUpdate: this.userActions.includes('settlement:receipt-management-update'),
      hasAuditReturn: this.userActions.includes('settlement:receipt-management-audit-return'),
    };
  }

  /**
   * 合并应收单数据到现有列表 - 直接追加新数据
   */
  private mergeReceivableData(newData: ReceivableReceiptDetail[]) {
    const existingData = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];

    // 创建唯一标识映射，避免重复数据
    const existingKeys = new Set<string>();
    existingData.forEach((item) => {
      const key = `${item.bills_receivable_code}_${item.outbound_code}_${item.order_code}_${item.po_code}`;
      existingKeys.add(key);
    });

    // 过滤掉重复的数据，只添加新的
    const filteredNewData = newData.filter((newItem) => {
      const key = `${newItem.bills_receivable_code}_${newItem.outbound_code}_${newItem.order_code}_${newItem.po_code}`;
      return !existingKeys.has(key);
    });

    // 直接追加新数据
    this.receivableTableConfig.dataList = [...existingData, ...filteredNewData];
  }

  /**
   * 合并出库单数据
   */
  private mergeOutboundData(existingReceivable: ReceivableReceiptDetail, newReceivable: ReceivableReceiptDetail) {
    if (!newReceivable.outbound_list) return;

    const existingOutboundMap = new Map<string, any>();

    // 建立现有出库单映射
    existingReceivable.outbound_list?.forEach((outbound) => {
      existingOutboundMap.set(outbound.outbound_code || '', outbound);
    });

    // 合并新的出库单数据
    newReceivable.outbound_list.forEach((newOutbound) => {
      const outboundCode = newOutbound.outbound_code || '';
      const existingOutbound = existingOutboundMap.get(outboundCode);

      if (existingOutbound) {
        // 出库单号一致，合并订单数据
        if (newOutbound.order_list) {
          existingOutbound.order_list = existingOutbound.order_list || [];
          existingOutbound.order_list.push(...newOutbound.order_list);
        }
      } else {
        // 新的出库单，直接添加
        existingReceivable.outbound_list = existingReceivable.outbound_list || [];
        existingReceivable.outbound_list.push(newOutbound);
      }
    });
  }

  /**
   * 更新应收单表格
   */
  private updateReceivableTable() {
    this.receivableTableConfig.count = this.receivableTableConfig.dataList.length;

    // 强制刷新表格
    this.receivableTableConfig = {
      ...this.receivableTableConfig,
      dataList: [...this.receivableTableConfig.dataList],
    };

    // 强制触发变更检测
    setTimeout(() => {
      // 简化的强制刷新，不依赖其他方法
      this.receivableTableConfig.loading = true;
      this.receivableTableConfig = { ...this.receivableTableConfig };

      setTimeout(() => {
        this.receivableTableConfig.loading = false;
        this.receivableTableConfig = { ...this.receivableTableConfig };
      }, 100);
    }, 0);
  }

  onDeleteAdvanceDetail(index: number) {
    this.advanceTableConfig.dataList.splice(index, 1);
    this.advanceTableConfig.count = this.advanceTableConfig.dataList.length;

    // 创建新的数组引用并强制刷新
    this.advanceTableConfig = {
      ...this.advanceTableConfig,
      dataList: [...this.advanceTableConfig.dataList],
    };

    // 如果删除后没有数据，强制刷新表格以正确显示空状态
  }

  onDeleteReceivableDetail(index: number) {
    this.receivableTableConfig.dataList.splice(index, 1);
    this.receivableTableConfig.count = this.receivableTableConfig.dataList.length;
    this.receivableTableConfig = { ...this.receivableTableConfig };
  }

  /* ==================== 计算相关 ==================== */

  onAdvanceRatioChange(item: AdvanceReceiptDetail, ratio: number) {
    item.payment = this._flcUtil.accMul(this._flcUtil.accDiv(ratio, 100), item.estimated_amount).toFixed(2);
    this.advanceTableConfig.dataList = this.advanceTableConfig.dataList.map((d) => {
      if (d.order_code === item.order_code) {
        d.payment = item.payment;
        d.payment_local = this.calculateExchangeRatePayment(item.payment);
      }
      return d;
    });
  }

  onAdvanceAmountChange(item: AdvanceReceiptDetail, amount: number) {
    const maxPayment = item.estimated_amount;
    if (amount > Number(maxPayment)) {
      this.message.warning('本次收款不可超过暂估金额');
    }
    item.payment_local = this.calculateExchangeRatePayment(amount, 2);
    item.ratio = this._flcUtil.accMul(this._flcUtil.accDiv(amount, item.estimated_amount), 100).toFixed(2);
    this.advanceTableConfig = { ...this.advanceTableConfig };
  }

  onAdvanceAmountLocalChange(item: AdvanceReceiptDetail, amount: number) {
    //  let exchangeRate = this.receiptForm.get('exchange_rate')?.value || 1;
    //     item.payment_local = this._flcUtil.accDiv(amount, exchangeRate).toFixed(2);
    //     this.advanceTableConfig = { ...this.advanceTableConfig };
  }

  /**
   * 应收单明细本次收款金额变更 - 增强验证逻辑
   */
  onReceivableCurrentAmountChange(item: ReceivableReceiptDetail, amount: number) {
    // 计算本次收款（本币）- 四舍五入保留2位小数
    item.current_receipt_amount_local = this.calculateExchangeRatePayment(amount, 2);
    // 刷新表格
    this.receivableTableConfig = { ...this.receivableTableConfig };
  }

  /**
   * 本次收款金额失焦验证
   */
  onBlurCurrentAmount(item: ReceivableReceiptDetail) {
    const currentAmount = parseFloat(item.current_receipt_amount || '0');
    const usedAdvanceAmount = parseFloat(item.used_advance_amount || '0');
    const receivableAmount = parseFloat(item.receivable_amount || '0');

    // 验证：本次收款+占用预收款必须小于等于应收金额
    if (this._flcUtil.accAdd(currentAmount, usedAdvanceAmount) > receivableAmount) {
      this.message.warning('本次收款+占用预收款必须小于等于应收金额');
      item.currentMark = true;
    } else {
      item.currentMark = false;
    }

    // 如果本次收款金额变更，需要重新计算可占用金额
    this.updateCanUsedAmountForOrder(item);
  }

  /**
   * 应收单明细本次收款（本币）金额变更
   */
  onReceivableCurrentAmountLocalChange(item: ReceivableReceiptDetail, localAmount: number) {
    // // 刷新表格
    // this.receivableTableConfig = { ...this.receivableTableConfig };
  }

  /**
   * 应收单明细占用预收款金额变更 - 基于 preReceiptAmount 和 canUsedAmount
   */
  onReceivableUsedAdvanceChange(item: ReceivableReceiptDetail, usedAmount: number) {
    const currentAmount = parseFloat(item.current_receipt_amount || '0');
    const receivableAmount = parseFloat(item.receivable_amount || '0');

    // 获取同订单的其他行数据（排除当前行）
    const otherSameOrderItems = this.receivableTableConfig.dataList.filter(
      (d: ReceivableReceiptDetail) => d.order_uuid === item.order_uuid && d !== item
    );

    // 判断其他同订单行的占用预收款是否都为0
    const otherItemsUsedAmountIsZero = otherSameOrderItems.every(
      (d: ReceivableReceiptDetail) => parseFloat(d.used_advance_amount || '0') === 0
    );

    // 计算当前行最大可占用金额
    let maxUsableForCurrentItem: number;

    if (otherItemsUsedAmountIsZero) {
      // 如果其他行占用预收款都为0，使用原始副本数据
      maxUsableForCurrentItem = this.originalCanUsedAmount[item.order_uuid] || 0;
    } else {
      // 否则使用当前可占用金额
      maxUsableForCurrentItem = this.canUsedAmount[item.order_uuid] || 0;
    }

    if (usedAmount > maxUsableForCurrentItem) {
      this.message.warning(`不得超过可占用预收款，当前最大可用：${maxUsableForCurrentItem}`);
      item.mark = true;
    } else {
      if (this._flcUtil.accAdd(currentAmount, usedAmount) > receivableAmount) {
        this.message.warning('本次收款+占用预收款必须小于等于应收金额');
        item.currentMark = true;
      } else {
        item.currentMark = false;
      }
      item.mark = false;
    }

    // 更新相关计算字段
    let amount = this._flcUtil.accSub(item.current_receipt_amount || 0, usedAmount);
    if (Number(amount) > 0) {
      item.current_receipt_amount = amount;
    }
    item.received_amount = this._flcUtil.accAdd(item!.current_receipt_amount || 0, usedAmount).toFixed(2);

    // 刷新表格
    this.receivableTableConfig = { ...this.receivableTableConfig };

    // 实时更新可占用金额
    this.updateCanUsedAmountForOrder(item);
  }

  /**
   * 自动占用预收款
   */
  onAutoUseAdvanceChange(autoUse: boolean) {
    if (autoUse) {
      // 自动计算占用预收款
      this.calculateAutoAdvanceUsage();
    } else {
      // 取消自动占用，清空占用预收款
      this.clearAutoAdvanceUsage();
    }

    // 刷新表格
    this.receivableTableConfig = { ...this.receivableTableConfig };
  }

  /**
   * 计算自动占用预收款 - 支持多出库单同订单的复杂场景
   * 业务规则：
   * 1. 当"应收金额"-"本次收款">0时才进行自动占用
   * 2. 若"应收金额"-"本次收款" >= "可占用"，则"占用预收款"自动带出"可占用"
   * 3. 若"应收金额"-"本次收款" < "可占用"，则"占用预收款"自动带出"应收金额"-"本次收款"
   * 4. 特殊情况：同订单多出库单需要依次计算，确保可占用金额正确分配
   */
  private calculateAutoAdvanceUsage() {
    const receivableDetails = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];

    // 重置可占用金额为初始状态
    this.resetCanUsedAmountToInitial();

    // 按订单分组处理，确保同订单的多个出库单依次计算
    const orderGroups = this.groupReceivableDetailsByOrder(receivableDetails);

    orderGroups.forEach((orderItems, orderUuid) => {
      let remainingCanUsed = this.canUsedAmount[orderUuid] || 0;

      orderItems.forEach((item) => {
        // 计算当前行的待收金额：应收金额 - 本次收款
        const pendingAmount = this._flcUtil.accSub(item.receivable_amount || 0, item.current_receipt_amount || 0);

        // 只有当待收金额 > 0 时才进行自动占用
        if (Number(pendingAmount) > 0 && remainingCanUsed > 0) {
          let autoUsedAmount = 0;

          // 根据业务规则计算自动占用金额
          if (Number(pendingAmount) >= remainingCanUsed) {
            // 待收金额 >= 可占用，则占用全部可占用金额
            autoUsedAmount = remainingCanUsed;
          } else {
            // 待收金额 < 可占用，则占用待收金额
            autoUsedAmount = Number(pendingAmount);
          }

          // 设置占用金额
          item.used_advance_amount = autoUsedAmount.toFixed(2);

          // 更新剩余可占用金额
          remainingCanUsed = this._flcUtil.accSub(remainingCanUsed, autoUsedAmount);

          // 验证：本次收款 + 占用预收款 <= 应收金额
          const totalAmount = this._flcUtil.accAdd(item.current_receipt_amount || 0, autoUsedAmount);
          if (totalAmount > Number(item.receivable_amount || 0)) {
            this.message.warning('本次收款+占用预收款必须小于等于应收金额');
            item.mark = true;
          } else {
            item.mark = false;
          }
        } else {
          // 不满足自动占用条件，清空占用金额
          item.used_advance_amount = '0';
          item.mark = false;
        }
      });

      // 更新该订单的可占用金额
      this.canUsedAmount[orderUuid] = remainingCanUsed;
    });

    // 刷新表格
    this.receivableTableConfig = { ...this.receivableTableConfig };
  }

  /**
   * 取消自动占用预收款
   */
  private clearAutoAdvanceUsage() {
    const receivableDetails = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];

    // 清空所有占用预收款金额
    receivableDetails.forEach((item) => {
      item.used_advance_amount = '0';
      item.mark = false;
    });

    // 重置可占用金额为初始状态
    this.resetCanUsedAmountToInitial();
  }

  /**
   * 重置可占用金额为初始状态
   */
  private resetCanUsedAmountToInitial() {
    // 重新计算每个订单的可占用金额
    Object.keys(this.preReceiptAmount).forEach(orderUuid => {
      this.canUsedAmount[orderUuid] = this.preReceiptAmount[orderUuid] || 0;
    });
  }

  /**
   * 按订单分组应收明细
   */
  private groupReceivableDetailsByOrder(receivableDetails: ReceivableReceiptDetail[]): Map<string, ReceivableReceiptDetail[]> {
    const orderGroups = new Map<string, ReceivableReceiptDetail[]>();

    receivableDetails.forEach((item) => {
      if (item.order_uuid) {
        if (!orderGroups.has(item.order_uuid)) {
          orderGroups.set(item.order_uuid, []);
        }
        orderGroups.get(item.order_uuid)!.push(item);
      }
    });

    return orderGroups;
  }

  /* ==================== 保存和提交 ==================== */
  translateKey(key: string): string {
    return this.translatePipe.transform(key);
  }

  /**
   * 保存收款单（暂存）
   */
  onSave() {
    this.onSubmit(1); // action_type = 1 表示暂存
  }

  /**
   * 提交收款单
   */
  onSubmit(action_type: number) {
    if (!this.validateForm()) {
      return;
    }

    this.saving = true;
    const submitData = this.buildSubmitData(action_type);

    this.receiptService
      .submitReceipt(submitData)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: any) => {
          if (res.code === 200) {
            this.message.success('提交成功');
            this.pageMode = PageEditStatusEnum.read;
            if (res.data.id) {
              this.receiptId = res.data.id;
              this.router.navigate(['../../list', res.data.id], { relativeTo: this.route });
            }
            // 重新加载数据以获取最新状态
            this.loadReceiptDetail();
          } else {
            this.message.error(res.message || '提交失败');
          }
          this.saving = false;
        },
        error: (error: any) => {
          console.error('提交失败:', error);
          this.message.error('提交失败');
          this.saving = false;
        },
      });
  }

  private validateForm(): boolean {
    if (this.validator.formIsInvalid(this.receiptForm)) {
      this.message.warning('请检查必填项');
      return false;
    }

    const receiptType = this.receiptForm.get('type')?.value;
    if (receiptType === ReceiptTypeEnum.advance) {
      if (this.advanceTableConfig.dataList.length === 0) {
        this.message.warning('请添加预收款明细');
        return false;
      }

      // 检查本次收款金额
      const invalidItems = this.advanceTableConfig.dataList.filter(
        (item: AdvanceReceiptDetail) => !item.payment || Number(item.payment) <= 0
      );

      // 检查本次收款（本币）金额
      const invalidLocalItems = this.advanceTableConfig.dataList.filter(
        (item: AdvanceReceiptDetail) => !item.payment_local || Number(item.payment_local) <= 0
      );

      if (invalidItems.length > 0) {
        this.message.warning('请填写本次收款金额');
        return false;
      }
      if(invalidLocalItems?.length > 0) {
        this.message.warning('请填写本次收款(本币)');
        return false;
      }
    } else if (receiptType === ReceiptTypeEnum.receivable) {
      if (this.receivableTableConfig.dataList.length === 0) {
        this.message.warning('请添加应收款明细');
        return false;
      }

      const hasMark = this.receivableTableConfig.dataList.filter((item) => item.mark || item.currentMark);
      if (hasMark.length > 0) {
        this.message.warning('本次收款+占用预收款必须小于等于应收金额');
        return false;
      }

      // 检查本次收款金额
      const invalidItems = this.receivableTableConfig.dataList.filter(
        (item: ReceivableReceiptDetail) => !item.current_receipt_amount || parseFloat(item.current_receipt_amount) <= 0
      );

      if (invalidItems.length > 0) {
        this.message.warning('请填写本次收款金额');
        return false;
      }

      // 验证预收款占用逻辑
      if (!this.validateAdvanceUsage()) {
        return false;
      }

      // 验证基于 preReceiptAmount 和 canUsedAmount 的数据一致性
      if (!this.validatePreReceiptAmountConsistency()) {
        return false;
      }
    }

    return true;
  }

  /**
   * 验证 preReceiptAmount 和 canUsedAmount 数据一致性
   */
  private validatePreReceiptAmountConsistency(): boolean {
    const receivableDetails = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];

    for (const item of receivableDetails) {
      if (!item.order_uuid) continue;

      // 检查是否有对应的预收款数据
      const preReceiptAmount = parseFloat(this.preReceiptAmount[item.order_uuid] || '0');
      const usedAdvanceAmount = parseFloat(item.used_advance_amount || '0');

      // 如果占用了预收款，但没有对应的预收款数据
      if (usedAdvanceAmount > 0 && preReceiptAmount <= 0) {
        this.message.error(`订单 ${item.order_code} 没有可用的预收款，无法占用`);
        return false;
      }

      // 检查占用金额是否超出可用金额
      if (usedAdvanceAmount > preReceiptAmount) {
        this.message.error(
          `订单 ${item.order_code} 占用预收款超出可用额度，可用：${preReceiptAmount.toFixed(2)}，占用：${usedAdvanceAmount.toFixed(2)}`
        );
        return false;
      }
    }

    return true;
  }

  /**
   * 构建提交数据 - 根据ReceiptDetailParams接口格式
   */
  private buildSubmitData(action_type: number): any {
    const formValue = this.receiptForm.value;
    const receiptType = this.receiptForm.get('type')?.value;

    // 基础数据
    const submitData: any = {
      ...formValue,
      action_type: action_type, // 1: 暂存, 2: 提交
      customer_id: Number(formValue.customer_id),
      account_id: Number(formValue.account_id),
      receipt_date: formValue.receipt_date ? new Date(formValue.receipt_date).setHours(0, 0, 0, 0) : Date.now(),
      currency_id: Number(formValue.currency_id || 1),
      exchange_rate: formValue.exchange_rate?.toString() || '1',
      remake: formValue.remark || '',
      payment_list: [], // 应收款列表，暂时传空数组
      pre_payment_list: [], // 预收款列表
    };

    // 根据收款类型填充对应的明细数据
    if (receiptType == ReceiptTypeEnum.advance) {
      // 预收款明细
      submitData.pre_payment_list = this.buildPrePaymentList();
    } else if (receiptType == ReceiptTypeEnum.receivable) {
      // 应收款明细
      submitData.payment_list = this.buildReceivablePaymentList();
    }

    return submitData;
  }

  /**
   * 构建预收款明细列表
   */
  private buildPrePaymentList(): any[] {
    const advanceDetails = this.advanceTableConfig.dataList as AdvanceReceiptDetail[];

    return advanceDetails.map((item) => {
      // 计算收款比例：本次收款 / 预收款总额 * 100
      const payment = item.payment?.toString() || '0';
      const payment_local = item.payment_local?.toString() || '0';
      const ratio = item.ratio.toString();

      return {
        order_uuid: item.order_uuid,
        current_receipt_amount: payment.toString(),
        current_receipt_amount_local: payment_local.toString(),
        ratio: ratio,
      };
    });
  }

  /**
   * 构建应收款明细列表 - 符合 ReceiptOrderSaveReqPayment 接口格式
   */
  private buildReceivablePaymentList(): any[] {
    const receivableDetails = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];

    const paymentList: any[] = [];

    receivableDetails.forEach((item) => {
      const paymentItem = {
        bills_receivable_id: item.bills_receivable_id,
        current_receipt_amount: String(item.current_receipt_amount),
        current_receipt_amount_local: String(item.current_receipt_amount_local),
        order_uuid: item.order_uuid,
        outbound_id: item.outbound_id,
        po_unique_code: item.po_unique_code,
        prepayment_occupied_amount: String(item.used_advance_amount), // 占用预收款
      };

      paymentList.push(paymentItem);
    });

    return paymentList;
  }

  /**
   * 获取客户名称
   */
  private getCustomerName(customerId: string): string {
    const customer = this.customerOptions.find((c) => c.id === customerId);
    return customer?.name || '';
  }

  onBack() {
    this.router.navigate(['../../list'], { relativeTo: this.route });
  }

  // 退回修改
  modify() {
    this.modalService
      .create({
        nzContent: ReceiptRefuseComponent,
        nzComponentParams: {
          title: '退回修改',
          flcErrorTip: '退回修改原因',
          placeholder: '请输入退回原因',
        },
        nzWidth: 400,
        nzClosable: false,
        nzWrapClassName: 'flc-confirm-modal',
        nzFooter: null,
      })
      .afterClose.subscribe((result: any) => {
        if (result?.success) {
          this.receiptService
            .passReceipt({ ids: [String(this.receiptId)], back_reason: result.reason, action_type: AuditActionType.refuse })
            .subscribe((res) => {
              if (res.code === 200) {
                this.message.success('退回修改成功');
                this.loadReceiptDetail();
              }
            });
        }
      });
  }

  pass() {
    this.receiptService.passReceipt({ ids: [String(this.receiptId)], action_type: AuditActionType.pass }).subscribe((res) => {
      if (res.code === 200) {
        this.message.success(this.translateKey('success.pass'));
        this.loadReceiptDetail();
      }
    });
  }

  payee() {
    this.receiptService.passReceipt({ ids: [String(this.receiptId)], action_type: AuditActionType.receive }).subscribe((res) => {
      if (res.code === 200) {
        this.message.success('收款成功');
        this.loadReceiptDetail();
      }
    });
  }

  cancel() {
    this.modalService
      .create({
        nzContent: ReceiptRefuseComponent,
        nzComponentParams: {
          title: '取消收款',
          flcErrorTip: '取消收款原因',
          placeholder: '请输入取消收款原因',
        },
        nzWidth: 400,
        nzClosable: false,
        nzWrapClassName: 'flc-confirm-modal',
        nzFooter: null,
      })
      .afterClose.subscribe((result: any) => {
        if (result?.success) {
          this.receiptService
            .passReceipt({ ids: [String(this.receiptId)], cancel_reason: result.reason, action_type: AuditActionType.cancel })
            .subscribe((res) => {
              if (res.code === 200) {
                this.message.success('取消成功');
                this.loadReceiptDetail();
              }
            });
        }
      });
  }

  /* ==================== 页面模式相关 ==================== */

  onSwitchToView() {
    this.pageMode = PageEditStatusEnum.read;
    this.loadReceiptDetail();
  }

  onSwitchToEdit() {
    this.pageMode = PageEditStatusEnum.edit;
  }

  /* ==================== 计算汇总 ==================== */

  get advanceSummary() {
    const dataList = this.advanceTableConfig.dataList as AdvanceReceiptDetail[];
    return {
      totalQty: dataList.reduce((sum, item) => this._flcUtil.accAdd(sum, item.order_qty), 0),
      totalEstimatedAmount: dataList.reduce((sum, item) => this._flcUtil.accAdd(sum, item.estimated_amount), 0),
      totalPreReceivingAmount: dataList.reduce((sum, item) => this._flcUtil.accAdd(sum, item.pre_receiving_amount), 0),
      totalPreReceivingAmountLocal: dataList.reduce((sum, item) => this._flcUtil.accAdd(sum, item.pre_receiving_amount_local), 0),
      totalReceivedAmount: dataList.reduce((sum, item) => this._flcUtil.accAdd(sum, item.received_amount), 0),
      totalReceivedAmountLocal: dataList.reduce((sum, item) => this._flcUtil.accAdd(sum, item.received_amount_local), 0),
      totalPayment: dataList.reduce((sum, item) => this._flcUtil.accAdd(sum, item.payment), 0),
      totalPaymentLocal: dataList.reduce((sum, item) => this._flcUtil.accAdd(sum, item.payment_local), 0),
      totalRadio: dataList.reduce((sum, item) => this._flcUtil.accAdd(sum, item.ratio), 0),
    };
  }

  get receivableSummary() {
    const dataList = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];

    let totalQuantity = 0;
    let totalReceivableAmount = 0;
    let totalReceivedAmount = 0;
    let totalPendingAmount = 0;
    let totalAdvanceAmount = 0;
    let totalUsedAdvanceAmount = 0;
    let totalCurrentReceiptAmount = 0;
    let totalCurrentReceiptAmountLocal = 0;

    dataList.forEach((item) => {
      totalQuantity = this._flcUtil.accAdd(totalQuantity, parseFloat(item.quantity || '0'));
      totalReceivableAmount = this._flcUtil.accAdd(totalReceivableAmount, parseFloat(item.receivable_amount || '0'));
      totalReceivedAmount = this._flcUtil.accAdd(totalReceivedAmount, parseFloat(item.received_amount || '0'));
      totalPendingAmount = this._flcUtil.accAdd(totalPendingAmount, parseFloat(item.pending_amount || '0'));
      totalAdvanceAmount = this._flcUtil.accAdd(totalAdvanceAmount, parseFloat(item.advance_amount || '0'));
      totalUsedAdvanceAmount = this._flcUtil.accAdd(totalUsedAdvanceAmount, parseFloat(item.used_advance_amount || '0'));
      totalCurrentReceiptAmount = this._flcUtil.accAdd(totalCurrentReceiptAmount, parseFloat(item.current_receipt_amount || '0'));
      totalCurrentReceiptAmountLocal = this._flcUtil.accAdd(totalCurrentReceiptAmountLocal, parseFloat(item.current_receipt_amount_local || '0'));
    });

    return {
      totalCount: dataList.length,
      totalQuantity: Number(totalQuantity.toFixed(0)),
      totalReceivableAmount: Number(totalReceivableAmount.toFixed(2)),
      totalReceivedAmount: Number(totalReceivedAmount.toFixed(2)),
      totalPendingAmount: Number(totalPendingAmount.toFixed(2)),
      totalAdvanceAmount: Number(totalAdvanceAmount.toFixed(2)),
      totalUsedAdvanceAmount: Number(totalUsedAdvanceAmount.toFixed(2)),
      totalCurrentReceiptAmount: Number(totalCurrentReceiptAmount.toFixed(2)),
      totalCurrentReceiptAmountLocal: Number(totalCurrentReceiptAmountLocal.toFixed(2)),
    };
  }

  /* ==================== 模板辅助方法 ==================== */

  /**
   * 更新订单的可占用金额
   */
  private updateCanUsedAmountForOrder(item: ReceivableReceiptDetail) {
    if (!item) return;
    let sameOrderItems = this.receivableTableConfig.dataList.filter((d) => d.order_uuid === item.order_uuid);
    let totalUsed = sameOrderItems.reduce((sum, d) => this._flcUtil.accAdd(sum, d.used_advance_amount || '0'), 0);
    this.canUsedAmount[item.order_uuid] = this._flcUtil.accSub(this.preReceiptAmount[item.order_uuid], totalUsed);
  }

  /**
   * 获取最大可占用预收款金额
   */
  getMaxUsableAdvance(item: ReceivableReceiptDetail): number {
    const totalAdvance = parseFloat(item.advance_amount || '0');
    const currentAmount = parseFloat(item.current_receipt_amount || '0');
    const receivableAmount = parseFloat(item.receivable_amount || '0');

    // 最大占用金额 = min(可占用预收款, 应收金额 - 本次收款)
    const maxByReceivable = Math.max(0, receivableAmount - currentAmount);
    return Math.min(totalAdvance, maxByReceivable);
  }

  /**
   * 获取最大本次收款金额
   */
  getMaxReceiptAmount(item: ReceivableReceiptDetail): number {
    const receivableAmount = parseFloat(item.receivable_amount || '0');
    const usedAdvance = parseFloat(item.used_advance_amount || '0');

    // 最大本次收款 = 应收金额 - 占用预收款
    return Math.max(0, receivableAmount - usedAdvance);
  }

  /**
   * 验证预收款占用的业务规则 - 基于 preReceiptAmount 和 canUsedAmount
   */
  private validateAdvanceUsage(): boolean {
    const receivableDetails = this.receivableTableConfig.dataList as ReceivableReceiptDetail[];
    const orderUsageMap = new Map<string, number>();

    // 统计每个订单的预收款占用总额
    receivableDetails.forEach((item) => {
      if (item.order_uuid) {
        const currentUsage = orderUsageMap.get(item.order_uuid) || 0;
        const itemUsage = parseFloat(item.used_advance_amount || '0');
        orderUsageMap.set(item.order_uuid, currentUsage + itemUsage);
      }
    });

    // 验证每个订单的占用不超过可用额度（基于 preReceiptAmount）
    for (const [orderUuid, totalUsage] of orderUsageMap) {
      const maxAdvance = parseFloat(this.preReceiptAmount[orderUuid] || '0');

      if (totalUsage > maxAdvance) {
        const orderItems = receivableDetails.filter((item) => item.order_uuid === orderUuid);
        const orderCode = orderItems.length > 0 ? orderItems[0].order_code : orderUuid;
        this.message.error(
          `订单 ${orderCode} 的预收款占用超出可用额度，最大可用：${maxAdvance.toFixed(2)}，当前占用：${totalUsage.toFixed(2)}`
        );
        return false;
      }

      // 验证本次收款 + 占用预收款 <= 应收金额
      const orderItems = receivableDetails.filter((item) => item.order_uuid === orderUuid);
      for (const item of orderItems) {
        const currentAmount = parseFloat(item.current_receipt_amount || '0');
        const usedAdvance = parseFloat(item.used_advance_amount || '0');
        const receivableAmount = parseFloat(item.receivable_amount || '0');

        if (currentAmount + usedAdvance > receivableAmount) {
          this.message.error(
            `订单 ${item.order_code} 的本次收款+占用预收款超出应收金额，应收：${receivableAmount.toFixed(
              2
            )}，本次收款：${currentAmount.toFixed(2)}，占用预收款：${usedAdvance.toFixed(2)}`
          );
          return false;
        }
      }
    }

    return true;
  }

  calculateExchangeRatePayment(amount: number | string, precision: number = 2, customExchangeRate?: number) {
    // 验证amount参数
    const numAmount = Number(amount);
    if (!amount || isNaN(numAmount) || !isFinite(numAmount)) {
      return '0.' + '0'.repeat(precision);
    }

    // 验证汇率参数 - 优先使用传入的汇率，否则使用表单中的汇率
    let exchangeRate = customExchangeRate !== undefined ? customExchangeRate : this.receiptForm.get('exchange_rate')?.value || 1;
    const numExchangeRate = Number(exchangeRate);
    if (!exchangeRate || isNaN(numExchangeRate) || !isFinite(numExchangeRate) || numExchangeRate <= 0) {
      exchangeRate = 1; // 默认汇率为1
    }

    // 执行除法计算
    const result = this._flcUtil.accDiv(numAmount, numExchangeRate);

    // 验证计算结果
    if (!isFinite(result) || isNaN(result)) {
      return '0.' + '0'.repeat(precision);
    }

    return result.toFixed(precision);
  }
}
