import { Observable, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { FlcSpKeyConstant, FlcSpUtilService } from 'fl-common-lib';
import {
  ReceiptDetail,
  ReceiptDetailParams,
  OrderSelectorParams,
  ReceivableSelectorParams,
  CustomerOption,
  AccountOption,
  ApiResponse,
  OrderSelectorItem,
  ReceivableSelectorItem,
  AuditParams,
  ReceivableReceiptDetail,
} from './models/receipt-detail.interface';

@Injectable()
export class RecerptService {
  private baseUrl = '/service/procurement-inventory/settlement/v1/receipt-order';
  userActions?: Array<string>;

  // 开发模式标识 - 设置为true时使用mock数据
  private isDevelopmentMode = false;

  constructor(private _http: HttpClient, private _spUtil: FlcSpUtilService) {}

  // TODO 切换权限
  getUserActions() {
    if (!this.userActions && this._spUtil.containsObjectKey(FlcSpKeyConstant.UserActions)) {
      const actionMap = this._spUtil.getObject(FlcSpKeyConstant.UserActions) as Map<string, []>;
      this.userActions = actionMap.get('settlement/receipt-management');
    }
    return this.userActions || [];
  }

  /* ==================== 收款单基础API ==================== */

  /**
   * 获取收款单详情
   */
  getDetail(id: string): Observable<ApiResponse<ReceiptDetail>> {
    return this._http.post<any>(`${this.baseUrl}/detail`, { id });
  }

  /**
   * 提交收款单
   */
  submitReceipt(data: ReceiptDetailParams): Observable<ApiResponse<any>> {
    return this._http.post<any>(`${this.baseUrl}/save`, data);
  }

  /**
   * 审核收款单 返回修改  确认收款
   */
  passReceipt(data: AuditParams): Observable<ApiResponse<any>> {
    // 设置为提交状态
    return this._http.post<any>(`${this.baseUrl}/edit`, data);
  }

  /**
   * 生成收款单号
   */
  generateReceiptCode(): Observable<ApiResponse<{ code: string }>> {
    return this._http.get<any>(`${this.baseUrl}/get-code`, {});
  }

  /* ==================== 基础数据API ==================== */

  /**
   * 获取客户档案选项
   */
  getCustomerOptions(keyword?: string): Observable<ApiResponse<{ list: CustomerOption[] }>> {
    return this._http.post<any>(`${this.baseUrl}/get-customer`, {
      name: keyword || '',
    });
  }

  /**
   * 获取账户管理选项
   */
  getAccountOptions(): Observable<ApiResponse<{ list: AccountOption[] }>> {
    return this._http.post<any>(`${this.baseUrl}/get-account`, {});
  }

  /**
   * 获取币种信息
   */
  getCurrencyOptions(payload: any): Observable<ApiResponse<any>> {
    return this._http.post<any>(`/service/archive/v1/api/unit/basic_option`, payload);
  }

  /* ==================== 预收单选择相关API ==================== */

  /**
   * 获取订单需求列表（用于预收款明细选择）
   */
  getOrderList(params: OrderSelectorParams): Observable<ApiResponse<{ list: OrderSelectorItem[]; total: number }>> {
    return this._http.post<any>(`${this.baseUrl}/order-list`, params);
  }

  /**
   * 获取订单需求筛选选项
   */
  getOrderListOptions(): Observable<ApiResponse<any>> {
    return this._http.post<any>(`${this.baseUrl}/order-list-option`, {});
  }

  /* ==================== 应收单选择相关API ==================== */

  /**
   * 获取应收单列表（用于应收款明细选择）
   */
  getReceivableList(params: ReceivableSelectorParams): Observable<ApiResponse<{ list: ReceivableSelectorItem[]; total: number }>> {
    return this._http.post<any>(`${this.baseUrl}/bills_receivable-list`, params);
  }

  /**
   * 获取应收单筛选选项
   */
  getReceivableListOptions(): Observable<ApiResponse<any>> {
    return this._http.post<any>(`${this.baseUrl}/bills_receivable-option`, {});
  }

  /**
   * 获取应收单明细
   */
  getReceivableDetail(payload: any): Observable<ApiResponse<ReceivableReceiptDetail>> {
    return this._http.post<any>(`${this.baseUrl}/bills_receivable-detail`, payload);
  }

  /* ==================== 其他API ==================== */

  /**
   * 获取收款单列表
   */
  getList(data: any): Observable<any> {
    return this._http.post<any>('/service/procurement-inventory/settlement/v1/receipt-order/list', data);
  }

  /**
   * 获取列表筛选选项
   */
  getListOptions(): Observable<any> {
    return this._http.post<any>('/service/procurement-inventory/settlement/v1/receipt-order/list-option', {});
  }

  
  /**
   * 批量编辑接口
   * @param data 传入数据
   * action_type: 1审核通过 ，2返回修改，3，确认收款 4.取消
   * back_reason: 返回修改原因
   * ids: 操作id
   * cancel_reason: 取消原因
   */
  batchReview(data: {
    action_type: 1 | 2 | 3 | 4,
    back_reason?: string,
    ids: number[],
    cancel_reason?: string
  }): Observable<any> {
    return this._http.post<any>('/service/procurement-inventory/settlement/v1/receipt-order/edit', data);
  }

  /**
   * 删除收款单
   */
  deleteReceipt(ids: number[]): Observable<ApiResponse<any>> {
    return this._http.post<any>(`/service/procurement-inventory/settlement/v1/receipt-order/delete`, {ids});
  }
}
