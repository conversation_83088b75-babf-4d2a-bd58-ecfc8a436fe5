import { FlcTableHeaderConfig } from 'fl-common-lib';
import { StockListOption, StockItem, StockSearchData, SearchConfig } from '../models/outstock.interface';
import { endOfDay, format, startOfDay } from 'date-fns';
import { OutboundStatusEnum } from '../models/outstock.enum';

export type StockItemTh = FlcTableHeaderConfig<StockItem>;

// 表头配置
export const defaultHeaders: StockItemTh[] = [
  {
    label: '状态',
    key: 'outbound_status_name',
    visible: true,
    width: '100px',
    type: 'text',
    pinned: true,
    disable: false,
    resizeble: true,
    style: getStatusColorStyle,
  },
  {
    label: '出库单号',
    key: 'code',
    visible: true,
    width: '160px',
    type: 'text',
    pinned: true,
    disable: false,
    resizeble: true,
  },
  {
    label: '出库类型',
    key: 'outbound_type_name',
    visible: true,
    width: '100px',
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
  },

  {
    label: '产品类型',
    key: 'product_type',
    visible: true,
    width: '100px',
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
    formatter: (item: StockItem) => ['', '成品', '半成品'][item.product_type],
  },
  {
    label: '客户',
    key: 'customer_name',
    visible: true,
    width: '100px',
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '供应商',
    key: 'supplier_name',
    visible: true,
    width: '100px',
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '款号',
    key: 'style_code',
    visible: true,
    width: '100px',
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '品名',
    key: 'category',
    visible: true,
    width: '100px',
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '关联单据',
    key: 'doc_codes_text',
    visible: true,
    width: '218px',
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '出库仓库',
    key: 'warehouse_name',
    visible: true,
    width: '170px',
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '出库总数',
    key: 'total_qty',
    visible: true,
    width: '120px',
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '出库时间',
    key: 'outbound_time',
    visible: true,
    width: '170px',
    type: 'datetime',
    sort: false,
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '创建人',
    key: 'gen_user_name',
    visible: true,
    width: '120px',
    type: 'text',
    pinned: false,
    disable: false,
    resizeble: true,
  },
  {
    label: '创建时间',
    key: 'gen_time',
    visible: true,
    width: '170px',
    type: 'datetime',
    sort: false,
    pinned: false,
    disable: false,
    resizeble: true,
  },
];

// 检索表单配置
export const searchConfig: SearchConfig<StockSearchData, StockListOption>[] = [
  {
    label: '出库单号',
    value: 'code',
    type: 'select',
    optionKey: 'codes',
  },
  {
    label: '出库类型',
    value: 'outbound_type',
    type: 'select',
    optionKey: 'outbound_types',
  },
  {
    label: '客户',
    value: 'customer_id',
    type: 'select',
    optionKey: 'customers',
  },
  {
    label: '供应商',
    value: 'supplier_id',
    type: 'select',
    optionKey: 'suppliers',
  },
  {
    label: '款号',
    value: 'style_code',
    type: 'select',
    optionKey: 'style_codes',
  },
  {
    label: '关联单据',
    value: 'doc_code',
    type: 'select',
    optionKey: 'doc_codes',
  },
  {
    label: '出库状态',
    value: 'outbound_status',
    type: 'select',
    optionKey: 'outbound_status',
  },
  {
    label: '出库时间',
    value: 'outbound_time',
    type: 'datarange',
  },
  {
    label: '出库仓库',
    value: 'warehouse_id',
    type: 'select',
    optionKey: 'warehouse_ids',
  },
  {
    label: '创建人',
    value: 'user_id',
    type: 'select',
    optionKey: 'user_ids',
  },
  {
    label: '创建时间',
    value: 'gen_time',
    type: 'datarange',
  },
];

// 格式化列表查询参数
export function formatParams(searchData: any): StockSearchData {
  const where = {
    ...searchData,
  };

  if (searchData?.outbound_time?.length) {
    where.outbound_time_start = format(startOfDay(searchData.outbound_time[0]), 'T');
    where.outbound_time_end = format(endOfDay(searchData.outbound_time[1]), 'T');
  }
  delete where.outbound_time;

  if (searchData?.gen_time?.length) {
    where.gen_time_start = format(startOfDay(searchData.gen_time[0]), 'T');
    where.gen_time_end = format(endOfDay(searchData.gen_time[1]), 'T');
  }
  delete where.gen_time;

  where.doc_type = where.doc_code?.type;
  where.doc_code = where.doc_code?.value;

  delete where.order_by;

  return where;
}

// 格式话列表数据
export function formatResponse(data: StockItem[]) {
  return data.map((d: StockItem) => ({
    ...d,
    doc_codes_text: d.doc_codes?.map((d: any) => d.doc_type_name + ':' + d.doc_code_name).join('、'),
  }));
}

export function initStatusOptions() {
  return [
    {
      label: '全部',
      value: 0,
      key: 'total_count',
      isTotalItem: true,
      checked: true,
      amount: 0,
      type: 'status',
    },
    { label: '待出库', value: OutboundStatusEnum['待出库'], key: 'wait_count', type: 'status' },
    { label: '已出库', value: OutboundStatusEnum['已出库'], key: 'done_count', type: 'status' },
  ];
}

function getStatusColorStyle(item: StockItem) {
  const styleObj: any = {};
  switch (item.outbound_status) {
    case OutboundStatusEnum['待出库']: //'待提交'
      styleObj.color = '#138AFF';
      break;
  }
  return styleObj;
}
