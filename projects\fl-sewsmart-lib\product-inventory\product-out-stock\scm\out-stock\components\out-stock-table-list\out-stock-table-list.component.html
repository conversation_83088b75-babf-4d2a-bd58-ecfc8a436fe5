<form [formGroup]="validateForm">
  <!-- 出库清单 -->
  <ng-container *ngIf="outBoundList.length; else noData">
    <nz-table
      class="zebra-striped-table"
      [nzData]="outBoundList.controls"
      [nzScroll]="{ y: tableHeight + 'px' }"
      [nzShowPagination]="false"
      nzBordered>
      <thead>
        <tr>
          <th nzWidth="36px" nzLeft>
            <a class="setting-btn" (click)="changeHeader($event)">
              <i nz-icon nzType="setting" nzTheme="fill"></i>
            </a>
          </th>
          <ng-container *ngFor="let item of renderHeaders">
            <th *ngIf="item.visible" [nzLeft]="item.pinned || false" [nzWidth]="item.width">
              <span [class]="{ 'required-th': item.required && isEdit }">{{ item.label }} </span>
            </th>
          </ng-container>

          <th nzWidth="70px" nzRight *ngIf="isEdit" nzRight>
            {{ 'flss.common.action' | translate }}
          </th>
        </tr>
      </thead>

      <tbody formArrayName="data_list">
        <tr *ngFor="let data of outBoundList.controls; let index = index" [formGroupName]="index">
          <td nzLeft>{{ index + 1 }}</td>

          <ng-container *ngFor="let item of renderHeaders; let i = index">
            <td *ngIf="item.visible" [nzLeft]="item.pinned || false">
              <flc-text-truncated *ngIf="item.type === 'text'" data="{{ data.get(item.key)?.value }}"></flc-text-truncated>
              <flc-text-truncated
                *ngIf="item.type === 'quantity'"
                [data]="data.get(item.key)?.value | number: '1.0-5'"></flc-text-truncated>

              <ng-container *ngIf="item.type == 'template'">
                <!-- 颜色 -->
                <flc-text-truncated *ngIf="item.key === 'color'" [data]="data.get(item.key)?.value?.color_name"></flc-text-truncated>
                <!-- 尺码 -->
                <flc-text-truncated *ngIf="item.key === 'size'" [data]="data.get(item.key)?.value?.spec_size"></flc-text-truncated>
                <!-- 货位 -->
                <flc-text-truncated *ngIf="item.key === 'location'" [data]="data.get(item.key)?.value?.location_name"> </flc-text-truncated>

                <!-- 出库数量 -->
                <ng-container *ngIf="item.key == 'outbound_qty'">
                  <nz-form-item *ngIf="isEdit; else readModeRef" style="margin: 8px 0">
                    <nz-form-control [nzErrorTip]="numErrorTpl">
                      <nz-input-number
                        flcStringifyFmCtrlValue
                        [formControlName]="item.key"
                        [nzPlaceHolder]="'flss.placeholder.input' | translate"
                        [nzMax]="*********"
                        [nzMin]="1"
                        [nzPrecision]="0"
                        nz-popconfirm
                        [nzPopconfirmTrigger]="null"
                        [nzPopconfirmVisible]="showConfirm && showConfirmRowIndex === index"
                        [nzPopconfirmTitle]="showConfirmTitle"
                        (ngModelChange)="onOutboundChange($event, index, data)"
                        (nzOnConfirm)="confirm()"
                        (nzOnCancel)="cancel(data)"
                        nzPopconfirmPlacement="top">
                      </nz-input-number>
                      <ng-template #numErrorTpl let-control>
                        <ng-container *ngIf="control.hasError('required')">
                          {{ translateSuffix + '请输入出库数量' | translate }}
                        </ng-container>
                        <ng-container *ngIf="control.hasError('greaterthan')">
                          {{ translateSuffix + '出库数量须大于0' | translate }}
                        </ng-container>
                        <ng-container *ngIf="control.hasError('max')">
                          {{ '不能超过' + (outboundOrderType === 1 ? '可出库数' : '库存数量') }}
                        </ng-container>
                      </ng-template>
                    </nz-form-control>
                  </nz-form-item>
                </ng-container>

                <!-- 箱数 -->
                <ng-container *ngIf="item.key == 'box_qty'">
                  <nz-form-item *ngIf="isEdit; else readModeRef" style="margin: 8px 0">
                    <nz-form-control [flcErrorTip]="item.label">
                      <nz-input-number
                        flcStringifyFmCtrlValue
                        [formControlName]="item.key"
                        [nzPlaceHolder]="'flss.placeholder.input' | translate"
                        [nzMax]="*********"
                        [nzMin]="1"
                        [nzPrecision]="0">
                      </nz-input-number>
                    </nz-form-control>
                  </nz-form-item>
                </ng-container>

                <!-- 含税单价 -->
                <ng-container *ngIf="item.key == 'unit_price'">
                  <nz-form-item *ngIf="isEdit; else readModeRef" style="margin: 8px 0">
                    <nz-form-control [flcErrorTip]="item.label">
                      <nz-input-number
                        flcStringifyFmCtrlValue
                        [formControlName]="item.key"
                        [nzPlaceHolder]="'flss.placeholder.input' | translate"
                        [nzMax]="*********.99"
                        [nzMin]="0"
                        [nzPrecision]="2"
                        (ngModelChange)="onTaxUnitPriceChange($event, index, data)">
                      </nz-input-number>
                    </nz-form-control>
                  </nz-form-item>
                </ng-container>

                <!-- 金额 -->
                <ng-container *ngIf="item.key == 'amount'">
                  <nz-form-item *ngIf="isEdit; else readModeRef" style="margin: 8px 0">
                    <nz-form-control>
                      <!-- <nz-input-number flcStringifyFmCtrlValue [formControlName]="item.key"  [nzPrecision]="2">
                      </nz-input-number> -->
                      <flc-text-truncated [data]="data.get(item.key)?.value"></flc-text-truncated>
                    </nz-form-control>
                  </nz-form-item>
                </ng-container>

                <!-- 箱号 -->
                <ng-container *ngIf="item.key == 'box_number'">
                  <nz-form-item *ngIf="isEdit; else readModeRef" style="margin: 8px 0">
                    <nz-form-control [flcErrorTip]="item.label">
                      <input
                        nz-input
                        style="width: 100%"
                        flcInputTrim
                        maxlength="20"
                        [formControlName]="item.key"
                        [placeholder]="'flss.placeholder.input' | translate" />
                    </nz-form-control>
                  </nz-form-item>
                </ng-container>

                <ng-template #readModeRef>
                  <flc-text-truncated [data]="data.get(item.key)?.value"></flc-text-truncated>
                </ng-template>
              </ng-container>
            </td>
          </ng-container>

          <td *ngIf="isEdit" nzRight>
            <button
              class="delete-hover"
              nz-button
              nzType="link"
              flButton="link"
              nz-popconfirm
              nzPopconfirmPlacement="left"
              [nzPopconfirmTitle]="translateSuffix + '确定删除？' | translate"
              (nzOnConfirm)="delOutBound(index, data)">
              <i nz-icon [nzIconfont]="'icon-caozuolan_shanchu1'"></i>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </ng-container>

  <ng-template #noData>
    <div class="no-data-content" [ngStyle]="{ height: tableHeight + 'px' }">
      <flc-no-data
        [btnText]="translateSuffix + '选择成品' | translate"
        [noDataTextTpl]="outbound_type && isEdit ? undefined : noDataTextTpl">
      </flc-no-data>
      <ng-template #noDataTextTpl>
        <span *ngIf="!isEdit">{{ 'flss.common.暂无数据' | translate }}</span>
        <span *ngIf="isEdit" style="color: #ff6e08">
          {{ translateSuffix + '请先选择提示' | translate }}
        </span>
      </ng-template>
    </div>
  </ng-template>
</form>
