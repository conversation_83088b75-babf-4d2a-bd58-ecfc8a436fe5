export const searchConfig = [
  { label: '应收单号', valueKey: 'bills_receivable_code', type: 'select', optionKey: 'bills_receivable_codes' },
  { label: '关联单据', valueKey: 'order_uuid', type: 'select', optionKey: 'order_codes' },
  { label: '关联发票', valueKey: 'bills_receivable_id', type: 'select', optionKey: 'sales_invoice_codes' },
  { label: '款号', valueKey: 'style_code', type: 'select', optionKey: 'style_codes' },
  // { label: '品名', valueKey: 'category', type: 'select', optionKey: 'order_codes' },
  { label: '客户', valueKey: 'customer_id', type: 'select', optionKey: 'customers' },
  { label: '应收日期', valueKey: 'receipt_time_range', type: 'date' },
  { label: '创建人', valueKey: 'created_by', type: 'select', optionKey: 'gen_user_names' },
  { label: '创建日期', valueKey: 'create_time_range', type: 'date' },
];

export const tableHeader = [
  {
    label: '应收单号',
    key: 'bills_receivable_code',
    visible: true,
    type: 'text',
    width: '140px',
    sort: false,
    pinned: false,
  },
  {
    label: '客户',
    key: 'customer_name',
    visible: true,
    type: 'text',
    width: '120px',
    sort: false,
    pinned: false,
  },
  {
    label: '关联发票',
    key: 'invoice_names',
    visible: true,
    type: 'text',
    width: '140px',
    sort: false,
    pinned: false,
  },
  {
    label: '关联单据',
    key: 'order_codes',
    visible: true,
    type: 'text',
    width: '140px',
    sort: false,
    pinned: false,
  },
  {
    label: '关联款号',
    key: 'style_codes',
    visible: true,
    type: 'text',
    width: '120px',
    sort: false,
    pinned: false,
  },
  {
    label: '币种',
    key: 'currency_name',
    visible: true,
    type: 'text',
    width: '80px',
    sort: false,
    pinned: false,
  },
  {
    label: '应收金额',
    key: 'expected_amount',
    visible: true,
    type: 'text',
    width: '120px',
    sort: false,
    pinned: false,
  },
  {
    label: '已收金额',
    key: 'received_amount',
    visible: true,
    type: 'text',
    width: '120px',
    sort: false,
    pinned: false,
  },
  {
    label: '待收金额',
    key: 'pending_amount',
    visible: true,
    type: 'text',
    width: '120px',
    sort: false,
    pinned: false,
  },
  {
    label: '应收日期',
    key: 'except_time',
    visible: true,
    type: 'template',
    templateName: 'except_time',
    width: '120px',
    sort: false,
    pinned: false,
  },
  {
    label: '创建人',
    key: 'create_name',
    visible: true,
    type: 'text',
    width: '100px',
    sort: false,
    pinned: false,
  },
  {
    label: '创建时间',
    key: 'create_time',
    visible: true,
    type: 'date',
    width: '140px',
    sort: false,
    pinned: false,
  },
];
